<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Additional Information Request Wizard -->
        <record id="view_hr_applicant_additional_info_wizard_form" model="ir.ui.view">
            <field name="name">hr.applicant.additional.info.wizard.form</field>
            <field name="model">hr.applicant.additional.info.wizard</field>
            <field name="arch" type="xml">
                <form string="Request Additional Information">
                    <div class="alert alert-info" role="alert">
                        <strong>Request Additional Information</strong><br/>
                        Use this form to request missing information or documents from the candidate.
                    </div>
                    
                    <group>
                        <field name="applicant_id" readonly="1"/>
                        <field name="send_email"/>
                    </group>
                    
                    <group string="Additional Information Required">
                        <field name="additional_info_text" nolabel="1" 
                               placeholder="Please specify what additional information or documents are needed from the candidate..."/>
                    </group>
                    
                    <footer>
                        <button name="action_send_request" string="Send Request" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
        <!-- Application Rejection Wizard -->
        <record id="view_hr_applicant_rejection_wizard_form" model="ir.ui.view">
            <field name="name">hr.applicant.rejection.wizard.form</field>
            <field name="model">hr.applicant.rejection.wizard</field>
            <field name="arch" type="xml">
                <form string="Reject Application">
                    <div class="alert alert-warning" role="alert">
                        <strong>Reject Application</strong><br/>
                        This action will reject the application at the Initial Qualification stage.
                    </div>
                    
                    <group>
                        <field name="applicant_id" readonly="1"/>
                        <field name="rejection_category"/>
                        <field name="send_email"/>
                    </group>
                    
                    <group string="Rejection Reason">
                        <field name="rejection_reason" nolabel="1" 
                               placeholder="Please provide a detailed reason for rejecting this application..."/>
                    </group>
                    
                    <footer>
                        <button name="action_reject_application" string="Reject Application" type="object" class="btn-danger"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
    </data>
</odoo>
