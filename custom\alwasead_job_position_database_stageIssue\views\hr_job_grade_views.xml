<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Job Grade Form View -->
        <record id="view_hr_job_grade_form" model="ir.ui.view">
            <field name="name">hr.job.grade.form</field>
            <field name="model">hr.job.grade</field>
            <field name="arch" type="xml">
                <form string="Job Grade">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_jobs" type="object" 
                                    class="oe_stat_button" icon="fa-briefcase">
                                <field name="job_count" widget="statinfo" string="Jobs"/>
                            </button>
                            <button name="action_view_employees" type="object" 
                                    class="oe_stat_button" icon="fa-users">
                                <field name="employee_count" widget="statinfo" string="Employees"/>
                            </button>
                        </div>
                        
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="code"/>
                                <field name="sequence"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="base_salary_multiplier"/>
                                <field name="default_contract_duration"/>
                                <field name="probation_period"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Experience Requirements" name="experience">
                                <group>
                                    <field name="min_experience_months"/>
                                    <field name="max_experience_months"/>
                                </group>
                            </page>
                            
                            <page string="Benefits" name="benefits">
                                <group>
                                    <field name="annual_leave_days"/>
                                    <field name="sick_leave_days"/>
                                </group>
                            </page>
                            
                            <page string="Description" name="description">
                                <field name="description" nolabel="1"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Job Grade Tree View -->
        <record id="view_hr_job_grade_tree" model="ir.ui.view">
            <field name="name">hr.job.grade.tree</field>
            <field name="model">hr.job.grade</field>
            <field name="arch" type="xml">
                <tree string="Job Grades">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="min_experience_months"/>
                    <field name="max_experience_months"/>
                    <field name="base_salary_multiplier"/>
                    <field name="job_count"/>
                    <field name="employee_count"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

    </data>
</odoo>
