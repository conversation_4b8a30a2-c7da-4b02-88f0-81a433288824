<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Enable hr.applicant model for website forms -->
        <record id="hr_recruitment.model_hr_applicant" model="ir.model">
            <field name="website_form_access">True</field>
            <field name="website_form_label">Submit Job Application</field>
        </record>
        
        <!-- Whitelist AlWasead custom fields for website forms -->
        <function model="ir.model.fields" name="formbuilder_whitelist">
            <value>hr.applicant</value>
            <value eval="[
                'partner_name',
                'email_from',
                'partner_mobile',
                'description',
                'job_id',
                'department_id',
                'nationality_id',
                'country_of_living_id',
                'academic_title',
                'work_experience_years',
                'recruiting_source',
                'diploma_attachment_id',
                'portfolio_attachment_id',
                'data_protection_consent',
            ]"/>
        </function>
        
    </data>
</odoo>
