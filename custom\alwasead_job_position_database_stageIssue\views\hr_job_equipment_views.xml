<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Job Equipment Form View -->
        <record id="view_hr_job_equipment_form" model="ir.ui.view">
            <field name="name">hr.job.equipment.form</field>
            <field name="model">hr.job.equipment</field>
            <field name="arch" type="xml">
                <form string="Job Equipment">
                    <sheet>
                        <group>
                            <group>
                                <field name="job_id"/>
                                <field name="equipment_type"/>
                                <field name="equipment_name"/>
                                <field name="quantity"/>
                                <field name="mandatory"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="estimated_cost"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="sequence"/>
                            </group>
                        </group>
                        
                        <group string="Description">
                            <field name="description" nolabel="1"/>
                        </group>
                        
                        <group string="Specifications">
                            <field name="specifications" nolabel="1"/>
                        </group>
                        
                        <notebook>
                            <page string="Assigned Employees" name="assignments">
                                <field name="assigned_to_employee_ids">
                                    <tree>
                                        <field name="name"/>
                                        <field name="job_id"/>
                                        <field name="department_id"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Job Equipment Tree View -->
        <record id="view_hr_job_equipment_tree" model="ir.ui.view">
            <field name="name">hr.job.equipment.tree</field>
            <field name="model">hr.job.equipment</field>
            <field name="arch" type="xml">
                <tree string="Job Equipment">
                    <field name="sequence" widget="handle"/>
                    <field name="job_id"/>
                    <field name="equipment_type"/>
                    <field name="equipment_name"/>
                    <field name="quantity"/>
                    <field name="mandatory"/>
                    <field name="estimated_cost"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

    </data>
</odoo>
