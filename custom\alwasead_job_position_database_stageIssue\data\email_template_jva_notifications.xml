<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- <PERSON><PERSON> Approved Email Template -->
        <record id="email_template_jva_approved" model="mail.template">
            <field name="name">JVA Approved Notification</field>
            <field name="model_id" ref="model_hr_jva_form"/>
            <field name="subject"><PERSON><PERSON> Approved: ${object.job_title}</field>
            <field name="email_from"><EMAIL></field>
            <field name="email_to">${object.requested_by.work_email}</field>
            <field name="description">Sent when <PERSON><PERSON> is approved</field>
            <field name="body_html">
                &lt;p&gt;Dear ${object.requested_by.name},&lt;/p&gt;
                &lt;p&gt;Great news! Your JVA form has been approved.&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;JVA Reference:&lt;/strong&gt; ${object.name}&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Job Position:&lt;/strong&gt; ${object.job_title}&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Approved Positions:&lt;/strong&gt; ${object.requested_positions}&lt;/p&gt;
                &lt;p&gt;The recruitment process can now begin.&lt;/p&gt;
                &lt;p&gt;Best regards,&lt;br/&gt;HR System&lt;/p&gt;
            </field>
        </record>

        <!-- JVA Rejected Email Template -->
        <record id="email_template_jva_rejected" model="mail.template">
            <field name="name">JVA Rejected Notification</field>
            <field name="model_id" ref="model_hr_jva_form"/>
            <field name="subject">JVA Rejected: ${object.job_title}</field>
            <field name="email_from"><EMAIL></field>
            <field name="email_to">${object.requested_by.work_email}</field>
            <field name="description">Sent when JVA is rejected</field>
            <field name="body_html">
                &lt;p&gt;Dear ${object.requested_by.name},&lt;/p&gt;
                &lt;p&gt;Your JVA form has been rejected.&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;JVA Reference:&lt;/strong&gt; ${object.name}&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Job Position:&lt;/strong&gt; ${object.job_title}&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Rejection Reason:&lt;/strong&gt; ${object.rejection_reason or 'Not specified'}&lt;/p&gt;
                &lt;p&gt;You can revise and resubmit the form after addressing the concerns.&lt;/p&gt;
                &lt;p&gt;Best regards,&lt;br/&gt;HR System&lt;/p&gt;
            </field>
        </record>
        
    </data>
</odoo>
