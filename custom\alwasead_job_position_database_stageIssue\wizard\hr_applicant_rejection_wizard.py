# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrApplicantRejectionWizard(models.TransientModel):
    _name = 'hr.applicant.rejection.wizard'
    _description = 'Reject Application at Initial Qualification'

    applicant_id = fields.Many2one(
        'hr.applicant',
        string='Applicant',
        required=True,
        readonly=True
    )
    
    rejection_reason = fields.Text(
        string='Rejection Reason',
        required=True,
        help="Specify the reason for rejecting this application"
    )
    
    rejection_category = fields.Selection([
        ('qualifications', 'Does not meet minimum qualifications'),
        ('experience', 'Insufficient relevant experience'),
        ('documents', 'Missing or invalid documents'),
        ('eligibility', 'Not eligible for this position'),
        ('other', 'Other reason')
    ], string='Rejection Category', required=True)
    
    send_email = fields.Boolean(
        string='Send Rejection Email to Candidate',
        default=True,
        help="Send a rejection email notification to the candidate"
    )

    def action_reject_application(self):
        """Reject the application"""
        self.ensure_one()
        
        if not self.rejection_reason:
            raise ValidationError(_("Please specify the reason for rejection."))
        
        # Format the rejection reason with category
        full_reason = f"[{dict(self._fields['rejection_category'].selection)[self.rejection_category]}] {self.rejection_reason}"
        
        # Call the applicant method to handle the rejection
        self.applicant_id._reject_application_initial(full_reason)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Application Rejected'),
                'message': _('Application rejected for: %s') % self.applicant_id.partner_name,
                'type': 'warning',
                'sticky': False,
            }
        }
