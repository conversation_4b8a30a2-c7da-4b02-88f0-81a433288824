# AlWasead Job Position Database - Requirements & Implementation Plan

## Project Overview
AlWasead requires a comprehensive Job Position Database system that enhances existing Odoo HR modules (hr.employee, hr.job, hr.recruitment) with company-specific organizational structure, automated workflows, and recruitment processes.

## Company Structure
AlWasead's organizational hierarchy:
- **General Manager's Office** (top level)
- **Divisions**: Engineering Affairs, Administrative Affairs, Finance, Supply
- **Departments**: Specialized departments within each division
- **Units/Teams**: Smaller subdivisions within departments (when needed)
- **Heads**: Each division, department, or unit has a designated head

## Job Category vs Job Grade System

AlWasead uses a dual classification system for positions:

### Job Categories (Classification)
**Purpose**: Define minimum educational requirements for candidate filtering
**Usage**: Used during recruitment to filter candidates who meet basic qualifications

| Classification | Requirements |
|---|---|
| **Engineer** | University degree in engineering |
| **Financial Officer** | University degree in economics, accounting or high diploma with minimum 10 years of experience |
| **Administrative Officer** | University degree of business administration or any other University degree with minimum 5 years of experience in the field, or high school degree with minimum 15 years of experience in field |
| **Operation Officer** | University degree in any field or high diploma with minimum 5 years of experience in the field, or high school degree with minimum 10 years of experience in field |
| **Technician** | High diploma in field or high school degree with minimum 5 years of experience in field |
| **Support Officer** | High school degree with minimum 5 years of experience |
| **Supervisor** | High diploma in field or high school with minimum 5 years of experience in fields |

### Job Grades (Experience Level)
**Purpose**: Define experience level for salary calculation and career progression
**Usage**: Used for salary calculations, contract terms, and internal ranking

| Job Grade | Requirements |
|---|---|
| **Trainee** | Fresh graduate - no experience |
| **Intern** | 6 months to 1 year of relevant work experience |
| **Junior** | 1 year to 5 years of relevant work experience |
| **Lead** | 5 years to 10 years of work experience |
| **Senior** | 10 years to 15 years of work experience |
| **Associate Consultant** | 15 years to 20 years of work experience |
| **Lead Consultant** | 20+ years to 25 years of work experience |

**Key Distinction:**
- **Job Category** = What type of role (education requirements)
- **Job Grade** = Experience level within that role (salary/progression)

Example: A position could be "Engineer" (category) at "Senior" (grade) level.

## Core Requirements

### 1. Job Position Database Foundation
- Extend `hr.job` model with comprehensive job description fields
- Integration with existing organizational structure
- Automated job posting and recruitment trigger system

### 2. Job Description Form Fields (extend hr.job)
**Based on AlWasead's Current Job Description Form:**

**Job Information Section:**
- **Job Title** (المسمى الوظيفي) - enhance existing `name` field
- **Division** (القسم) - new field linking to division model
- **Department** (الإدارة) - enhance existing `department_id`
- **Unit/Team** (الوحدة / الفريق) - new field for sub-organizational level
- **Number of Positions in the Team** (عدد المناصب في الفريق) - new `required_positions` field
- **Direct Supervisor** (المشرف المباشر) - new `supervisor_id` field
- **Place of Work** (مكان العمل) - new field with Office/Site selection

**Job Description Section:**
- **Job Overview** (نظرة عامة على الوظيفة) - enhance existing `description` field
  - "One clear paragraph summarizing the primary mission of the job"
- **Key Responsibilities** (المسؤوليات الرئيسية) - new `responsibilities` field
  - "List of 6-12 specific tasks and outcomes"
- **Key Competencies** (الكفاءات الرئيسية) - new `competencies` field
  - "Key skills for the position to perform"
- **Authorities and Decision Rights** (السلطات وحقوق اتخاذ القرار) - new `authorities` field
  - "What the position can decide or approve"
- **Required Educational Background** (الخلفية التعليمية المطلوبة) - new `education_background` field
  - "Degrees, certifications, special licenses"
- **Required Technical Skills** (المهارات التقنية المطلوبة) - new `technical_skills` field
  - "Software, tools, procedures, standards"
- **Key Performance Indicators (KPIs)** (مؤشرات الأداء الرئيسية) - new `kpis` field
- **Working Conditions / Environment / Risk Exposure** (ظروف العمل/البيئة/التعرض للمخاطر) - new `working_conditions` field

**Assets / Equipment and Access Credentials Section:**
- **Assets / Equipment** (الأصول / المعدات) - One2many relation to equipment model
- **Software / Platform Access** (الوصول للبرمجيات / المنصات) - One2many relation to access model

**Minimum Requirements Section:**
**Pre-defined Job Categories with Education Requirements:**
- ☐ **Engineer** - University degree in engineering
- ☐ **Financial Officer** - University degree in economics, accounting, or high diploma with minimum 10 years experience
- ☐ **Administrative Officer** - University degree in business administration or other field with minimum 5 years experience, or high school with minimum 15 years experience
- ☐ **Operation Officer** - University degree in any field with high diploma and minimum 5 years experience, or high school with minimum 10 years experience
- ☐ **Technician** - High diploma or high school with minimum 5 years experience
- ☐ **Support Officer** - High school with minimum 5 years experience
- ☐ **Supervisor** - High diploma or high school with minimum 5 years experience

**Approval Workflow Section:**
- **Prepared By** (من إعداد) - auto-filled with current user
- **Signature & Date** (التوقيع والتاريخ) - auto-filled with timestamp
- **Authority Levels for Approval:**
  - **Head of Department** (رئيس القسم)
  - **Head of Division/Office** (رئيس القسم/المكتب)
  - **General Manager Authentication** (مصادقة المدير العام)

### 3. Automated Workflows & Notifications

**Staffing Level Monitoring:**
- Automatic tracking of current vs required positions
- Real-time vacancy calculation
- Automated notifications to department heads when understaffed

**JVA (Job Vacancy Announcement) Process:**
- Auto-generation of JVA forms when positions are vacant
- Integration with existing ardano_approval module
- Workflow: Department Head → Approval → Job Advertisement

**Recruitment Integration:**
- Seamless integration with existing cubes_recruitment module
- Automatic job advertisement generation for external platforms
- Candidate filtering based on job category requirements
- Job grade assignment during hiring process

### 4. Enhanced Models Structure

**Extend hr.job:**
- All job description form fields
- Organizational hierarchy links
- Staffing level tracking
- Equipment and access requirements
- Automated workflow triggers

**Extend hr.employee:**
- Link to specific job position
- Equipment assignments tracking
- Access credentials management
- Performance against job KPIs

**Extend hr.recruitment/hr.applicant:**
- Job category filtering
- Job grade assignment
- JVA form reference
- Enhanced evaluation criteria

**New Supporting Models:**
- Job Category (education levels, filtering criteria)
- Job Grade (salary multipliers, contract terms)
- Job Location (office/site, location factors)
- Equipment Assignment (per job position)
- Access Credentials (per job position)
- Staffing Level Tracker (automated monitoring)
- JVA Form (vacancy announcements)

### 5. Integration Points

**With Existing Modules:**
- `ardano_hr_customization`: Enhance organizational structure
- `cubes_recruitment`: Integrate evaluation and hiring process
- `ardano_approval`: JVA form approval workflows

**Automated Processes:**
- Staffing level monitoring (scheduled actions)
- Department head notifications (email/system alerts)
- Job advertisement generation (automated content creation)
- Salary calculations (location factors + job grades)
- Equipment allocation (onboarding automation)

### 6. Key Features

**For HR Managers:**
- Comprehensive job position management
- Real-time staffing level dashboards
- Automated recruitment triggering
- Equipment and access tracking

**For Department Heads:**
- Staffing level alerts and notifications
- JVA form creation and approval
- Team structure management
- Performance tracking against job requirements

**For Recruitment Team:**
- Automated job advertisements
- Candidate filtering by job category
- Integrated evaluation process
- Job offer generation with correct salary calculations

**For Employees:**
- Clear job descriptions and expectations
- Equipment and access assignments
- Performance indicators and career progression paths

## Implementation Approach
1. **Extend existing HR models** rather than creating separate systems
2. **Integrate with current modules** (ardano_hr_customization, cubes_recruitment, ardano_approval)
3. **Maintain backward compatibility** with existing data and processes
4. **Implement automated workflows** for efficiency and consistency
5. **Provide comprehensive reporting** and analytics

## Detailed JVA Phase Automation Requirements

### Overview
The JVA (Job Vacancy Announcement) Phase is critical for ensuring recruitment aligns with company staffing needs and is initiated only when there's an approved organizational requirement.

### Automated Staffing Level Monitoring

**Core Logic:**
- Each position has predefined "Number of Positions in Team" (from Job Description Form)
- System continuously tracks: Target Number vs Actual Number of employees
- Automated comparison must be real-time and always up-to-date

**Trigger Conditions:**
```
IF (Actual Employees < Target Positions) THEN
    Trigger JVA Process
```

**Implementation Requirements:**
- Scheduled action to run staffing level checks (every hour/daily)
- Real-time updates when employees are hired/terminated/transferred
- Dashboard showing staffing gaps across all positions

### Automated Notification System

**When Staffing Gap Detected:**
1. **Automatic notification** sent to Head of Department
2. **Notification Content:**
   - Position details (Job Title, Department, Unit)
   - Current staffing level vs required
   - Suggested number of positions to recruit
   - Direct link to create JVA Form

**Notification Channels:**
- Email notification
- Odoo system notification/inbox
- Dashboard alert

### JVA Form Structure & Automation

**JVA Form Model (new model: hr.jva.form):**

**Auto-populated Fields (from Job Description Form):**
- Job Title
- Division
- Department
- Unit/Team
- Required Minimum Requirements
- Job Category
- Direct Supervisor

**Department Head Input Fields:**
- **Number of Positions Requested** (auto-calculated, manually adjustable)
  - Default calculation: `Target Positions - Current Employees`
  - Department Head can modify if needed
- **Additional Desired Qualifications** (beyond minimum requirements)
- **Required Language Knowledge & Level**
- **Required Experience Level** (Job Grade system):
  - Trainee
  - Intern
  - Junior
  - Lead
  - Senior
  - Associate Consultant
  - Lead Consultant
- **Justification/Comments**
- **Priority Level** (High/Medium/Low)
- **Expected Start Date**

### JVA Approval Workflow - Detailed Process

**Workflow States:**
1. **Draft** - Department Head creating form (editable)
2. **Approval Pending** - Submitted to General Manager (read-only)
3. **Approved** - Ready for recruitment (read-only)
4. **Rejected** - Returned to Department Head (editable after reset)
5. **Cancelled** - No longer needed

**Detailed Approval Process:**

**Step 1: JVA Form Completion**
- Department Head completes all required fields in JVA Form
- System validates all mandatory fields are filled
- "Request Approval" button becomes available only when form is complete

**Step 2: Submission Trigger**
- Department Head clicks "Request Approval" button
- System immediately triggers automated workflow:
  - JVA Form status changes to "Approval Pending"
  - Form becomes read-only (no further changes allowed)
  - Automated email sent to General Manager with form details
  - System notification created in General Manager's inbox
  - Dashboard alert appears for General Manager

**Step 3: General Manager Review**
- General Manager receives notification with complete JVA data:
  - Requested number of positions
  - Additional qualifications
  - Required experience level (Job Grade)
  - Language requirements
  - All auto-populated job data from Job Description Form
- System presents two action buttons:
  - **"Approve JVA"** button
  - **"Reject JVA"** button

**Step 4: Approval Decision**

**If APPROVED:**
- Status immediately changes to "Approved"
- Automated notifications sent to:
  - Recruiter (can now begin recruitment process)
  - Department Head (confirmation of approval)
- Job advertisement creation becomes available
- Recruitment pipeline is unlocked

**If REJECTED:**
- Status immediately changes to "Rejected"
- General Manager must provide rejection reason
- Automated notifications sent to:
  - Department Head (with rejection reason)
  - Recruiter (recruitment blocked)
- Form can be reset to "Draft" for revision
- New approval process required for any recruitment

**Business Rules & System Constraints:**
- **No recruitment actions** can proceed without "Approved" status
- **Job advertisements** can only be created after JVA approval
- **Recruitment pipeline** completely blocked until approval
- **Form editing** only allowed in "Draft" or "Rejected" states
- **Status visibility** always displayed on form header
- **Audit trail** maintained for all approval actions
- **Automatic notifications** for all status changes

**Technical Implementation Requirements:**
- Approval status field always visible on form
- State-based button visibility (Request Approval only in Draft)
- Form field readonly when not in editable states
- Email templates for all notification scenarios
- Dashboard widgets for pending approvals
- Integration with existing ardano_approval module

### Integration with Existing Modules

**With cubes_recruitment:**
- Approved JVA Forms automatically create recruitment records
- Job advertisements inherit JVA criteria
- Candidate evaluation uses JVA requirements

**With ardano_approval:**
- JVA approval workflow uses existing approval infrastructure
- Approval history and audit trail
- Configurable approval levels if needed

### Technical Implementation

**Models to Extend:**
```python
# Extend hr.job with AlWasead Job Description Form fields
class HrJob(models.Model):
    _inherit = 'hr.job'

    # Job Information Section
    division_id = fields.Many2one('hr.division', 'Division (القسم)')
    unit_id = fields.Many2one('hr.unit', 'Unit/Team (الوحدة/الفريق)')
    required_positions = fields.Integer('Number of Positions in Team (عدد المناصب في الفريق)')
    supervisor_id = fields.Many2one('hr.employee', 'Direct Supervisor (المشرف المباشر)')
    place_of_work = fields.Selection([
        ('office', 'Office (المكتب)'),
        ('site', 'Site (الموقع)')
    ], 'Place of Work (مكان العمل)')

    # Job Description Section
    job_overview = fields.Text('Job Overview (نظرة عامة على الوظيفة)')
    key_responsibilities = fields.Text('Key Responsibilities (المسؤوليات الرئيسية)')
    key_competencies = fields.Text('Key Competencies (الكفاءات الرئيسية)')
    authorities_decisions = fields.Text('Authorities and Decision Rights (السلطات وحقوق اتخاذ القرار)')
    education_background = fields.Text('Required Educational Background (الخلفية التعليمية المطلوبة)')
    technical_skills = fields.Text('Required Technical Skills (المهارات التقنية المطلوبة)')
    kpis = fields.Text('Key Performance Indicators (مؤشرات الأداء الرئيسية)')
    working_conditions = fields.Text('Working Conditions/Environment/Risk (ظروف العمل/البيئة/المخاطر)')

    # Job Category (Minimum Requirements) - Used for candidate filtering
    job_category = fields.Selection([
        ('engineer', 'Engineer - University degree in engineering'),
        ('financial_officer', 'Financial Officer - University degree in economics/accounting or high diploma + 10 years exp'),
        ('admin_officer', 'Administrative Officer - University degree in business admin + 5 years exp or high school + 15 years exp'),
        ('operation_officer', 'Operation Officer - University degree + high diploma + 5 years exp or high school + 10 years exp'),
        ('technician', 'Technician - High diploma or high school + 5 years exp'),
        ('support_officer', 'Support Officer - High school + 5 years exp'),
        ('supervisor', 'Supervisor - High diploma or high school + 5 years exp')
    ], 'Job Category (فئة الوظيفة)')

    # Job Grade (Experience Level) - Used for salary calculation and career progression
    job_grade = fields.Selection([
        ('trainee', 'Trainee - Fresh graduate, no experience'),
        ('intern', 'Intern - 6 months to 1 year of relevant work experience'),
        ('junior', 'Junior - 1 year to 5 years of relevant work experience'),
        ('lead', 'Lead - 5 years to 10 years of work experience'),
        ('senior', 'Senior - 10 years to 15 years of work experience'),
        ('associate_consultant', 'Associate Consultant - 15 years to 20 years of work experience'),
        ('lead_consultant', 'Lead Consultant - 20+ years to 25 years of work experience')
    ], 'Job Grade (درجة الوظيفة)')

    # Assets and Access
    equipment_ids = fields.One2many('hr.job.equipment', 'job_id', 'Required Equipment (المعدات المطلوبة)')
    access_ids = fields.One2many('hr.job.access', 'job_id', 'Software/Platform Access (الوصول للبرمجيات)')

    # Staffing Level Tracking
    current_employees = fields.Integer('Current Employees', compute='_compute_current_employees')
    vacant_positions = fields.Integer('Vacant Positions', compute='_compute_vacant_positions')
    staffing_status = fields.Selection([
        ('adequate', 'Adequately Staffed'),
        ('understaffed', 'Understaffed'),
        ('overstaffed', 'Overstaffed')
    ], compute='_compute_staffing_status')

    # Approval Tracking
    prepared_by = fields.Many2one('res.users', 'Prepared By (من إعداد)', default=lambda self: self.env.user)
    prepared_date = fields.Datetime('Prepared Date', default=fields.Datetime.now)
    approved_by_dept_head = fields.Many2one('hr.employee', 'Approved by Head of Department')
    approved_by_division_head = fields.Many2one('hr.employee', 'Approved by Head of Division')
    approved_by_general_manager = fields.Many2one('hr.employee', 'Approved by General Manager')
    approval_state = fields.Selection([
        ('draft', 'Draft'),
        ('dept_approved', 'Department Head Approved'),
        ('division_approved', 'Division Head Approved'),
        ('final_approved', 'General Manager Approved')
    ], 'Approval Status', default='draft')
```

**Supporting Models:**
```python
# Equipment Assignment Model
class HrJobEquipment(models.Model):
    _name = 'hr.job.equipment'
    _description = 'Job Position Required Equipment'

    job_id = fields.Many2one('hr.job', 'Job Position', required=True, ondelete='cascade')
    equipment_type = fields.Selection([
        ('laptop', 'Laptop'),
        ('desktop', 'Desktop Computer'),
        ('phone', 'Mobile Phone'),
        ('vehicle', 'Vehicle'),
        ('tools', 'Specialized Tools'),
        ('other', 'Other Equipment')
    ], 'Equipment Type', required=True)
    equipment_name = fields.Char('Equipment Name/Model')
    description = fields.Text('Description')
    mandatory = fields.Boolean('Mandatory', default=True)

# Software/Platform Access Model
class HrJobAccess(models.Model):
    _name = 'hr.job.access'
    _description = 'Job Position Required Software/Platform Access'

    job_id = fields.Many2one('hr.job', 'Job Position', required=True, ondelete='cascade')
    access_type = fields.Selection([
        ('software', 'Software Application'),
        ('platform', 'Online Platform'),
        ('system', 'Internal System'),
        ('database', 'Database Access'),
        ('network', 'Network Access'),
        ('other', 'Other Access')
    ], 'Access Type', required=True)
    system_name = fields.Char('System/Software Name', required=True)
    access_level = fields.Selection([
        ('read', 'Read Only'),
        ('write', 'Read/Write'),
        ('admin', 'Administrator'),
        ('full', 'Full Access')
    ], 'Access Level')
    description = fields.Text('Description')
    mandatory = fields.Boolean('Mandatory', default=True)

# Division Model (if not exists)
class HrDivision(models.Model):
    _name = 'hr.division'
    _description = 'Company Division'

    name = fields.Char('Division Name', required=True)
    code = fields.Char('Division Code')
    head_id = fields.Many2one('hr.employee', 'Division Head')
    description = fields.Text('Description')
    active = fields.Boolean('Active', default=True)

# Unit Model (enhance existing)
class HrUnit(models.Model):
    _inherit = 'hr.unit'

    head_id = fields.Many2one('hr.employee', 'Unit Head')
    division_id = fields.Many2one('hr.division', 'Division')

# Job Grade Model for salary calculations
class HrJobGrade(models.Model):
    _name = 'hr.job.grade'
    _description = 'Job Grade for Experience Level and Salary Calculation'
    _order = 'sequence, name'

    name = fields.Char('Grade Name', required=True)
    code = fields.Char('Grade Code', required=True)
    sequence = fields.Integer('Sequence', default=10)
    description = fields.Text('Description')

    # Experience requirements
    min_experience_months = fields.Integer('Minimum Experience (Months)')
    max_experience_months = fields.Integer('Maximum Experience (Months)')

    # Salary calculation
    base_salary_multiplier = fields.Float('Base Salary Multiplier', default=1.0,
                                        help='Multiplier applied to base salary for this grade')

    # Contract terms
    default_contract_duration = fields.Integer('Default Contract Duration (Months)', default=12)
    probation_period = fields.Integer('Probation Period (Months)', default=3)

    # Benefits
    annual_leave_days = fields.Integer('Annual Leave Days', default=21)
    sick_leave_days = fields.Integer('Sick Leave Days', default=14)

    active = fields.Boolean('Active', default=True)

    _sql_constraints = [
        ('code_unique', 'unique(code)', 'Grade code must be unique!'),
        ('name_unique', 'unique(name)', 'Grade name must be unique!')
    ]

# Job Category Model for education requirements
class HrJobCategory(models.Model):
    _name = 'hr.job.category'
    _description = 'Job Category for Education Requirements and Candidate Filtering'
    _order = 'sequence, name'

    name = fields.Char('Category Name', required=True)
    code = fields.Char('Category Code', required=True)
    sequence = fields.Integer('Sequence', default=10)
    description = fields.Text('Description')

    # Education requirements
    education_requirements = fields.Text('Education Requirements', required=True)
    minimum_education_level = fields.Selection([
        ('high_school', 'High School'),
        ('diploma', 'High Diploma'),
        ('bachelor', 'Bachelor Degree'),
        ('master', 'Master Degree'),
        ('phd', 'PhD')
    ], 'Minimum Education Level')

    # Experience requirements for this category
    minimum_experience_years = fields.Integer('Minimum Experience Years')
    required_field_experience = fields.Boolean('Requires Field-Specific Experience', default=False)

    active = fields.Boolean('Active', default=True)

    _sql_constraints = [
        ('code_unique', 'unique(code)', 'Category code must be unique!'),
        ('name_unique', 'unique(name)', 'Category name must be unique!')
    ]
```

**New JVA Model:**
```python
class HrJvaForm(models.Model):
    _name = 'hr.jva.form'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Job Vacancy Announcement Form'

    name = fields.Char('JVA Reference', required=True, default=lambda self: self.env['ir.sequence'].next_by_code('hr.jva.form'))

    # Auto-populated from Job Description Form
    job_id = fields.Many2one('hr.job', 'Job Position', required=True)
    job_title = fields.Char('Job Title', related='job_id.name', readonly=True)
    division_id = fields.Many2one('hr.division', 'Division', related='job_id.division_id', readonly=True)
    department_id = fields.Many2one('hr.department', 'Department', related='job_id.department_id', readonly=True)
    unit_id = fields.Many2one('hr.unit', 'Unit/Team', related='job_id.unit_id', readonly=True)
    job_category = fields.Selection(related='job_id.job_category', readonly=True)
    minimum_requirements = fields.Text('Minimum Requirements', related='job_id.education_background', readonly=True)

    # Department Head Input Fields
    requested_positions = fields.Integer('Number of Positions Requested', required=True)
    suggested_positions = fields.Integer('Suggested Positions', compute='_compute_suggested_positions')
    additional_qualifications = fields.Text('Additional Desired Qualifications')
    language_requirements = fields.Text('Required Language Knowledge & Level')
    experience_level = fields.Selection([
        ('trainee', 'Trainee'),
        ('intern', 'Intern'),
        ('junior', 'Junior'),
        ('lead', 'Lead'),
        ('senior', 'Senior'),
        ('associate_consultant', 'Associate Consultant'),
        ('lead_consultant', 'Lead Consultant')
    ], 'Required Experience Level')
    justification = fields.Text('Justification/Comments')
    priority = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent')
    ], 'Priority Level', default='medium')
    expected_start_date = fields.Date('Expected Start Date')

    # Workflow Fields
    state = fields.Selection([
        ('draft', 'Draft'),
        ('approval_pending', 'Approval Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled')
    ], 'Status', default='draft', tracking=True)

    # Tracking Fields
    requested_by = fields.Many2one('hr.employee', 'Requested By', default=lambda self: self.env.user.employee_id)
    request_date = fields.Datetime('Request Date', default=fields.Datetime.now)
    submitted_date = fields.Datetime('Submitted Date')
    reviewed_by = fields.Many2one('hr.employee', 'Reviewed By (General Manager)')
    review_date = fields.Datetime('Review Date')
    approved_by = fields.Many2one('hr.employee', 'Approved By')
    approval_date = fields.Datetime('Approval Date')
    rejected_by = fields.Many2one('hr.employee', 'Rejected By')
    rejection_date = fields.Datetime('Rejection Date')
    rejection_reason = fields.Text('Rejection Reason')

    # Computed Fields
    can_request_approval = fields.Boolean('Can Request Approval', compute='_compute_can_request_approval')
    is_editable = fields.Boolean('Is Editable', compute='_compute_is_editable')

    @api.depends('state', 'requested_positions', 'job_id')
    def _compute_can_request_approval(self):
        for record in self:
            record.can_request_approval = (
                record.state == 'draft' and
                record.requested_positions > 0 and
                record.job_id
            )

    @api.depends('state')
    def _compute_is_editable(self):
        for record in self:
            record.is_editable = record.state in ('draft', 'rejected')

    def action_request_approval(self):
        """Submit JVA for General Manager approval"""
        if not self.can_request_approval:
            raise UserError(_('JVA Form is not ready for approval. Please complete all required fields.'))

        self.write({
            'state': 'approval_pending',
            'submitted_date': fields.Datetime.now()
        })

        # Send notification to General Manager
        self._send_approval_notification()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('JVA Submitted'),
                'message': _('JVA Form has been submitted to General Manager for approval.'),
                'type': 'success'
            }
        }

    def action_approve(self):
        """General Manager approves the JVA"""
        if self.state != 'approval_pending':
            raise UserError(_('Only pending JVA forms can be approved.'))

        self.write({
            'state': 'approved',
            'approved_by': self.env.user.employee_id.id,
            'approval_date': fields.Datetime.now()
        })

        # Notify Recruiter and Department Head
        self._send_approval_result_notification(approved=True)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('JVA Approved'),
                'message': _('JVA Form has been approved. Recruitment can now proceed.'),
                'type': 'success'
            }
        }

    def action_reject(self):
        """General Manager rejects the JVA"""
        if self.state != 'approval_pending':
            raise UserError(_('Only pending JVA forms can be rejected.'))

        # Open wizard for rejection reason
        return {
            'type': 'ir.actions.act_window',
            'name': _('Reject JVA'),
            'res_model': 'hr.jva.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_jva_id': self.id}
        }

    def action_reset_to_draft(self):
        """Reset rejected JVA to draft for revision"""
        if self.state != 'rejected':
            raise UserError(_('Only rejected JVA forms can be reset to draft.'))

        self.write({
            'state': 'draft',
            'rejection_reason': False,
            'rejected_by': False,
            'rejection_date': False
        })

    def _send_approval_notification(self):
        """Send email notification to General Manager"""
        # Implementation for email notification
        pass

    def _send_approval_result_notification(self, approved=True):
        """Send notification after approval/rejection"""
        # Implementation for result notifications
        pass

    @api.depends('job_id', 'job_id.required_positions', 'job_id.current_employees')
    def _compute_suggested_positions(self):
        for record in self:
            if record.job_id:
                record.suggested_positions = max(0, record.job_id.required_positions - record.job_id.current_employees)
            else:
                record.suggested_positions = 0
```

**Automated Actions:**
- Scheduled action for staffing level monitoring
- Email templates for notifications
- Workflow automation for approvals

### Dashboard & Reporting

**Department Head Dashboard:**
- Current staffing levels for all positions
- Pending JVA forms
- Recruitment pipeline status

**General Manager Dashboard:**
- Company-wide staffing overview
- Pending JVA approvals
- Recruitment metrics

**HR Dashboard:**
- All active JVA forms
- Recruitment pipeline
- Staffing analytics

## Next Steps
- Detailed technical specification for model extensions
- Database migration scripts for existing data
- User interface enhancements for new fields
- JVA workflow implementation
- Integration testing with existing modules
- User training and documentation

---
*This document serves as the foundation for implementing AlWasead's Job Position Database requirements within the existing Odoo HR framework.*
