# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class HrJvaRejectWizard(models.TransientModel):
    _name = 'hr.jva.reject.wizard'
    _description = 'JVA Rejection Wizard'

    jva_id = fields.Many2one(
        'hr.jva.form',
        string='JVA Form',
        required=True,
        readonly=True
    )
    rejection_reason = fields.Text(
        string='Rejection Reason',
        required=True,
        help="Please provide a detailed reason for rejecting this JVA form"
    )

    def action_reject_jva(self):
        """Reject the JVA form with reason"""
        if not self.rejection_reason:
            raise UserError(_('Rejection reason is required.'))

        self.jva_id.write({
            'state': 'rejected',
            'rejected_by': self.env.user.employee_id.id,
            'rejection_date': fields.Datetime.now(),
            'rejection_reason': self.rejection_reason
        })

        # Send notification
        self.jva_id._send_approval_result_notification(approved=False)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('<PERSON><PERSON> Rejected'),
                'message': _('JVA Form has been rejected and returned to the Department Head.'),
                'type': 'warning'
            }
        }
