# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrJobAccess(models.Model):
    _name = 'hr.job.access'
    _description = 'Job Position Required Software/Platform Access'
    _order = 'sequence, access_type, system_name'

    job_id = fields.Many2one(
        'hr.job',
        string='Job Position',
        required=True,
        ondelete='cascade'
    )
    sequence = fields.Integer(string='Sequence', default=10)
    
    access_type = fields.Selection([
        ('software', 'Software Application'),
        ('platform', 'Online Platform'),
        ('system', 'Internal System'),
        ('database', 'Database Access'),
        ('network', 'Network Access'),
        ('email', 'Email System'),
        ('cloud', 'Cloud Service'),
        ('other', 'Other Access')
    ], string='Access Type', required=True)
    
    system_name = fields.Char(
        string='System/Software Name',
        required=True,
        help="Name of the system or software"
    )
    description = fields.Text(
        string='Description',
        help="Additional details about the access requirements"
    )
    
    access_level = fields.Selection([
        ('read', 'Read Only'),
        ('write', 'Read/Write'),
        ('admin', 'Administrator'),
        ('full', 'Full Access'),
        ('custom', 'Custom Access')
    ], string='Access Level', default='read')
    
    access_details = fields.Text(
        string='Access Details',
        help="Specific details about the required access level"
    )
    
    mandatory = fields.Boolean(
        string='Mandatory',
        default=True,
        help="Whether this access is mandatory for the position"
    )
    
    # License tracking
    requires_license = fields.Boolean(
        string='Requires License',
        default=False,
        help="Whether this access requires a paid license"
    )
    license_cost = fields.Monetary(
        string='License Cost (Monthly)',
        currency_field='currency_id',
        help="Monthly cost for the license"
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    # Assignment tracking
    assigned_to_employee_ids = fields.Many2many(
        'hr.employee',
        'hr_job_access_employee_rel',
        'access_id',
        'employee_id',
        string='Assigned to Employees',
        help="Employees who have been granted this access"
    )
    
    # Security and compliance
    security_level = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical')
    ], string='Security Level', default='medium')
    
    requires_training = fields.Boolean(
        string='Requires Training',
        default=False,
        help="Whether training is required before granting access"
    )
    training_details = fields.Text(
        string='Training Details',
        help="Details about required training"
    )
    
    active = fields.Boolean(string='Active', default=True)

    _sql_constraints = [
        ('license_cost_positive', 'check(license_cost >= 0)', 'License cost cannot be negative!')
    ]

    def name_get(self):
        """Custom name display"""
        result = []
        for access in self:
            name = f"{access.system_name}"
            if access.access_type:
                name = f"[{dict(access._fields['access_type'].selection)[access.access_type]}] {name}"
            if access.access_level:
                name = f"{name} ({dict(access._fields['access_level'].selection)[access.access_level]})"
            result.append((access.id, name))
        return result

    def action_assign_to_employees(self):
        """Open wizard to assign access to employees"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Assign Access'),
            'res_model': 'hr.job.access.assign.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_access_id': self.id,
                'default_job_id': self.job_id.id
            }
        }

    @api.onchange('requires_license')
    def _onchange_requires_license(self):
        """Clear license cost if not required"""
        if not self.requires_license:
            self.license_cost = 0.0

    @api.onchange('requires_training')
    def _onchange_requires_training(self):
        """Clear training details if not required"""
        if not self.requires_training:
            self.training_details = False
