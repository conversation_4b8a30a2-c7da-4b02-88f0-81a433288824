<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Job Access Form View -->
        <record id="view_hr_job_access_form" model="ir.ui.view">
            <field name="name">hr.job.access.form</field>
            <field name="model">hr.job.access</field>
            <field name="arch" type="xml">
                <form string="Job Access">
                    <sheet>
                        <group>
                            <group>
                                <field name="job_id"/>
                                <field name="access_type"/>
                                <field name="system_name"/>
                                <field name="access_level"/>
                                <field name="mandatory"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="security_level"/>
                                <field name="requires_license"/>
                                <field name="license_cost" attrs="{'invisible': [('requires_license', '=', False)]}"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="sequence"/>
                            </group>
                        </group>
                        
                        <group string="Description">
                            <field name="description" nolabel="1"/>
                        </group>
                        
                        <group string="Access Details">
                            <field name="access_details" nolabel="1"/>
                        </group>
                        
                        <group string="Training" attrs="{'invisible': [('requires_training', '=', False)]}">
                            <field name="requires_training"/>
                            <field name="training_details" attrs="{'invisible': [('requires_training', '=', False)]}"/>
                        </group>
                        
                        <notebook>
                            <page string="Assigned Employees" name="assignments">
                                <field name="assigned_to_employee_ids">
                                    <tree>
                                        <field name="name"/>
                                        <field name="job_id"/>
                                        <field name="department_id"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Job Access Tree View -->
        <record id="view_hr_job_access_tree" model="ir.ui.view">
            <field name="name">hr.job.access.tree</field>
            <field name="model">hr.job.access</field>
            <field name="arch" type="xml">
                <tree string="Job Access">
                    <field name="sequence" widget="handle"/>
                    <field name="job_id"/>
                    <field name="access_type"/>
                    <field name="system_name"/>
                    <field name="access_level"/>
                    <field name="security_level" widget="badge"/>
                    <field name="mandatory"/>
                    <field name="requires_license"/>
                    <field name="license_cost"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

    </data>
</odoo>
