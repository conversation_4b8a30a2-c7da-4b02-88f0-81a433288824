<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Enhanced Employee Form View -->
        <record id="view_hr_employee_form_enhanced" model="ir.ui.view">
            <field name="name">hr.employee.form.enhanced</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='department_id']" position="after">
                    <field name="division_ids" widget="many2many_tags"/>
                    <field name="unit_ids" widget="many2many_tags" domain="[('department_id', '=', department_id)]"/>
                </xpath>
                
                <xpath expr="//field[@name='job_id']" position="after">
                    <field name="job_category_id"/>
                    <field name="job_grade_id"/>
                    <field name="is_general_manager" string="General Manager"/>
                </xpath>
                
                <xpath expr="//page[@name='hr_settings']" position="after">
                    <page string="Equipment &amp; Access" name="equipment_access">
                        <group>
                            <group string="Equipment">
                                <field name="equipment_count"/>
                                <button name="action_view_equipment" type="object" 
                                        string="View Equipment" class="oe_link"
                                        attrs="{'invisible': [('equipment_count', '=', 0)]}"/>
                                <button name="action_assign_equipment" type="object" 
                                        string="Assign Equipment" class="oe_link"/>
                            </group>
                            <group string="Software Access">
                                <field name="access_count"/>
                                <button name="action_view_access" type="object" 
                                        string="View Access" class="oe_link"
                                        attrs="{'invisible': [('access_count', '=', 0)]}"/>
                                <button name="action_assign_access" type="object" 
                                        string="Assign Access" class="oe_link"/>
                            </group>
                        </group>
                        
                        <group string="Salary Calculation">
                            <field name="location_factor"/>
                            <field name="grade_multiplier"/>
                        </group>
                        
                        <group string="Current KPIs">
                            <field name="current_kpis" nolabel="1"/>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Employee Tree View -->
        <record id="view_hr_employee_tree_enhanced" model="ir.ui.view">
            <field name="name">hr.employee.tree.enhanced</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='department_id']" position="after">
                    <field name="division_ids" widget="many2many_tags"/>
                    <field name="unit_ids" widget="many2many_tags"/>
                    <field name="job_grade_id"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
