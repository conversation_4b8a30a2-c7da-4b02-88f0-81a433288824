<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Main Menu -->
        <menuitem id="menu_alwasead_job_position_database" 
                  name="Job Position Database" 
                  parent="hr.menu_hr_root" 
                  sequence="15"/>

        <!-- Job Positions Menu -->
        <menuitem id="menu_job_positions" 
                  name="Job Positions" 
                  parent="menu_alwasead_job_position_database" 
                  sequence="10"/>

        <menuitem id="menu_hr_job_enhanced"
                  name="Job Positions"
                  parent="menu_job_positions"
                  action="hr_recruitment.action_hr_job"
                  sequence="10"/>

        <!-- JVA Forms Menu -->
        <menuitem id="menu_jva_forms" 
                  name="JVA Forms" 
                  parent="menu_alwasead_job_position_database" 
                  sequence="20"/>

        <menuitem id="menu_hr_jva_form_all" 
                  name="All JVA Forms" 
                  parent="menu_jva_forms" 
                  action="action_hr_jva_form" 
                  sequence="10"/>

        <menuitem id="menu_hr_jva_form_my"
                  name="My JVA Requests"
                  parent="menu_jva_forms"
                  action="action_hr_jva_form_my"
                  sequence="20"/>

        <menuitem id="menu_hr_jva_form_pending"
                  name="Pending Approvals"
                  parent="menu_jva_forms"
                  action="action_hr_jva_form_pending"
                  sequence="30"
                  groups="alwasead_job_position_database.group_jva_approver"/>

        <!-- Configuration Menu -->
        <menuitem id="menu_job_position_config" 
                  name="Configuration" 
                  parent="menu_alwasead_job_position_database" 
                  sequence="90"/>



        <menuitem id="menu_hr_job_category"
                  name="Job Categories"
                  parent="menu_job_position_config"
                  action="action_hr_job_category"
                  sequence="10"/>

        <!-- Job Grades -->
        <record id="action_hr_job_grade" model="ir.actions.act_window">
            <field name="name">Job Grades</field>
            <field name="res_model">hr.job.grade</field>
            <field name="view_mode">tree,form</field>
        </record>

        <menuitem id="menu_hr_job_grade" 
                  name="Job Grades" 
                  parent="menu_job_position_config" 
                  action="action_hr_job_grade" 
                  sequence="20"/>

        <!-- AlWasead Divisions -->
        <record id="action_hr_division" model="ir.actions.act_window">
            <field name="name">AlWasead Divisions</field>
            <field name="res_model">hr.division</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create AlWasead's organizational divisions
                </p>
                <p>
                    Divisions are the top level of AlWasead's organizational structure:
                    Engineering Affairs, Administrative Affairs, Finance, and Supply.
                </p>
            </field>
        </record>

        <menuitem id="menu_hr_division"
                  name="AlWasead Divisions"
                  parent="menu_job_position_config"
                  action="action_hr_division"
                  sequence="30"/>

        <!-- Units -->
        <record id="action_hr_unit" model="ir.actions.act_window">
            <field name="name">Units/Teams</field>
            <field name="res_model">hr.unit</field>
            <field name="view_mode">tree,form</field>
        </record>

        <menuitem id="menu_hr_unit"
                  name="Units/Teams"
                  parent="menu_job_position_config"
                  action="action_hr_unit"
                  sequence="35"/>

        <!-- Equipment -->
        <record id="action_hr_job_equipment" model="ir.actions.act_window">
            <field name="name">Job Equipment</field>
            <field name="res_model">hr.job.equipment</field>
            <field name="view_mode">tree,form</field>
        </record>

        <menuitem id="menu_hr_job_equipment" 
                  name="Equipment" 
                  parent="menu_job_position_config" 
                  action="action_hr_job_equipment" 
                  sequence="40"/>

        <!-- Access -->
        <record id="action_hr_job_access" model="ir.actions.act_window">
            <field name="name">Job Access</field>
            <field name="res_model">hr.job.access</field>
            <field name="view_mode">tree,form</field>
        </record>

        <menuitem id="menu_hr_job_access" 
                  name="Software Access" 
                  parent="menu_job_position_config" 
                  action="action_hr_job_access" 
                  sequence="50"/>



    </data>
</odoo>
