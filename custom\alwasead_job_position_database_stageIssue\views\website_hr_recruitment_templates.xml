<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Enhanced Job Application Form Template -->
        <template id="apply_job_form_enhanced" name="Enhanced Job Application Form" 
                  inherit_id="website_hr_recruitment.apply">
            
            <!-- Add AlWasead specific fields to the application form -->
            <xpath expr="//form[@id='hr_recruitment_form']//div[hasclass('s_website_form_rows')]" position="inside">
                
                <!-- Nationality Field -->
                <div class="col-12 mb-0 py-2 s_website_form_field s_website_form_required" 
                     data-type="many2one" data-name="Field">
                    <div class="row s_col_no_resize s_col_no_bgcolor">
                        <label class="col-4 col-sm-auto s_website_form_label" style="width: 200px">
                            <span class="s_website_form_label_content">Nationality</span>
                            <span class="s_website_form_mark"> *</span>
                        </label>
                        <div class="col-sm">
                            <select name="nationality_id" class="form-control s_website_form_input" required="required">
                                <option value="">Select Nationality...</option>
                                <t t-foreach="request.env['res.country'].sudo().search([('name', '!=', False)], order='name')" t-as="country">
                                    <option t-att-value="country.id" t-esc="country.name"/>
                                </t>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Country of Living Field -->
                <div class="col-12 mb-0 py-2 s_website_form_field s_website_form_required" 
                     data-type="many2one" data-name="Field">
                    <div class="row s_col_no_resize s_col_no_bgcolor">
                        <label class="col-4 col-sm-auto s_website_form_label" style="width: 200px">
                            <span class="s_website_form_label_content">Country of Living</span>
                            <span class="s_website_form_mark"> *</span>
                        </label>
                        <div class="col-sm">
                            <select name="country_of_living_id" class="form-control s_website_form_input" required="required">
                                <option value="">Select Country...</option>
                                <t t-foreach="request.env['res.country'].sudo().search([('name', '!=', False)], order='name')" t-as="country">
                                    <option t-att-value="country.id" t-esc="country.name"/>
                                </t>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Academic Title Field -->
                <div class="col-12 mb-0 py-2 s_website_form_field s_website_form_required" 
                     data-type="selection" data-name="Field">
                    <div class="row s_col_no_resize s_col_no_bgcolor">
                        <label class="col-4 col-sm-auto s_website_form_label" style="width: 200px">
                            <span class="s_website_form_label_content">Academic Title (Degree)</span>
                            <span class="s_website_form_mark"> *</span>
                        </label>
                        <div class="col-sm">
                            <select name="academic_title" class="form-control s_website_form_input" required="required">
                                <option value="">Select Academic Title...</option>
                                <option value="high_school">High School Diploma</option>
                                <option value="diploma">Diploma</option>
                                <option value="bachelor">Bachelor's Degree</option>
                                <option value="master">Master's Degree</option>
                                <option value="phd">PhD/Doctorate</option>
                                <option value="professional">Professional Certificate</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Years of Experience Field -->
                <div class="col-12 mb-0 py-2 s_website_form_field s_website_form_required" 
                     data-type="float" data-name="Field">
                    <div class="row s_col_no_resize s_col_no_bgcolor">
                        <label class="col-4 col-sm-auto s_website_form_label" style="width: 200px">
                            <span class="s_website_form_label_content">Years of Relevant Work Experience</span>
                            <span class="s_website_form_mark"> *</span>
                        </label>
                        <div class="col-sm">
                            <input type="number" name="work_experience_years" 
                                   class="form-control s_website_form_input" 
                                   step="0.5" min="0" max="50" 
                                   placeholder="e.g., 2.5" required="required"/>
                        </div>
                    </div>
                </div>
                
                <!-- Source of Application Field -->
                <div class="col-12 mb-0 py-2 s_website_form_field s_website_form_required" 
                     data-type="selection" data-name="Field">
                    <div class="row s_col_no_resize s_col_no_bgcolor">
                        <label class="col-4 col-sm-auto s_website_form_label" style="width: 200px">
                            <span class="s_website_form_label_content">Source of Application</span>
                            <span class="s_website_form_mark"> *</span>
                        </label>
                        <div class="col-sm">
                            <select name="recruiting_source" class="form-control s_website_form_input" required="required">
                                <option value="">How did you learn about this position?</option>
                                <option value="website">Company Website</option>
                                <option value="linkedin">LinkedIn</option>
                                <option value="facebook">Facebook</option>
                                <option value="libyanjob">LibyanJob</option>
                                <option value="mage">Service Provider - Mage</option>
                                <option value="pavo">Service Provider - Pavo</option>
                                <option value="art">Service Provider - Art</option>
                                <option value="mnw">Service Provider - MNW</option>
                                <option value="referral">Employee Referral</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Diploma Upload Field -->
                <div class="col-12 mb-0 py-2 s_website_form_field s_website_form_required" 
                     data-type="binary" data-name="Field">
                    <div class="row s_col_no_resize s_col_no_bgcolor">
                        <label class="col-4 col-sm-auto s_website_form_label" style="width: 200px">
                            <span class="s_website_form_label_content">Diploma/Degree Certificate</span>
                            <span class="s_website_form_mark"> *</span>
                        </label>
                        <div class="col-sm">
                            <input type="file" name="diploma_attachment_id" 
                                   class="form-control s_website_form_input" 
                                   accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" 
                                   required="required"/>
                            <small class="form-text text-muted">
                                Upload your diploma or degree certificate (PDF, Image, or Word document)
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Portfolio Upload Field (Optional) -->
                <div class="col-12 mb-0 py-2 s_website_form_field" 
                     data-type="binary" data-name="Field">
                    <div class="row s_col_no_resize s_col_no_bgcolor">
                        <label class="col-4 col-sm-auto s_website_form_label" style="width: 200px">
                            <span class="s_website_form_label_content">Portfolio (Optional)</span>
                        </label>
                        <div class="col-sm">
                            <input type="file" name="portfolio_attachment_id" 
                                   class="form-control s_website_form_input" 
                                   accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.zip"/>
                            <small class="form-text text-muted">
                                Upload your portfolio if applicable (for design, development, or creative positions)
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Data Protection Disclaimer -->
                <div class="col-12 mb-0 py-2 s_website_form_field s_website_form_required" 
                     data-type="boolean" data-name="Field">
                    <div class="row s_col_no_resize s_col_no_bgcolor">
                        <div class="col-12">
                            <div class="alert alert-info" role="alert">
                                <h5><i class="fa fa-shield"></i> Data Protection Disclaimer</h5>
                                <p>
                                    By submitting this application, you agree that AlWasead may collect, process, 
                                    and store your personal data for recruitment purposes only. Your data will be:
                                </p>
                                <ul class="mb-3">
                                    <li>Processed confidentially and securely</li>
                                    <li>Used solely for recruitment and selection purposes</li>
                                    <li>Not shared with third parties without your explicit consent</li>
                                    <li>Retained for a maximum of 2 years after the recruitment process</li>
                                    <li>Subject to your right to request access, correction, or deletion</li>
                                </ul>
                                <div class="form-check">
                                    <input type="checkbox" name="data_protection_consent" 
                                           class="form-check-input s_website_form_input" 
                                           id="data_protection_consent" value="1" required="required"/>
                                    <label class="form-check-label" for="data_protection_consent">
                                        <strong>I agree to the data protection terms and consent to the processing of my personal data for recruitment purposes.</strong>
                                        <span class="s_website_form_mark"> *</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
            </xpath>
            
        </template>
        
    </data>
</odoo>
