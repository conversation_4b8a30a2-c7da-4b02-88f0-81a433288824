# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrJobEquipment(models.Model):
    _name = 'hr.job.equipment'
    _description = 'Job Position Required Equipment'
    _order = 'sequence, equipment_type, equipment_name'

    job_id = fields.Many2one(
        'hr.job',
        string='Job Position',
        required=True,
        ondelete='cascade'
    )
    sequence = fields.Integer(string='Sequence', default=10)
    
    equipment_type = fields.Selection([
        ('laptop', 'Laptop'),
        ('desktop', 'Desktop Computer'),
        ('phone', 'Mobile Phone'),
        ('vehicle', 'Vehicle'),
        ('tools', 'Specialized Tools'),
        ('safety', 'Safety Equipment'),
        ('office', 'Office Equipment'),
        ('other', 'Other Equipment')
    ], string='Equipment Type', required=True)
    
    equipment_name = fields.Char(
        string='Equipment Name/Model',
        required=True,
        help="Specific name or model of the equipment"
    )
    description = fields.Text(
        string='Description',
        help="Additional details about the equipment requirements"
    )
    specifications = fields.Text(
        string='Specifications',
        help="Technical specifications or requirements"
    )
    
    mandatory = fields.Boolean(
        string='Mandatory',
        default=True,
        help="Whether this equipment is mandatory for the position"
    )
    quantity = fields.Integer(
        string='Quantity',
        default=1,
        help="Number of units required"
    )
    
    # Cost tracking
    estimated_cost = fields.Monetary(
        string='Estimated Cost',
        currency_field='currency_id',
        help="Estimated cost for this equipment"
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    # Assignment tracking
    assigned_to_employee_ids = fields.Many2many(
        'hr.employee',
        'hr_job_equipment_employee_rel',
        'equipment_id',
        'employee_id',
        string='Assigned to Employees',
        help="Employees who have been assigned this equipment"
    )
    
    active = fields.Boolean(string='Active', default=True)

    _sql_constraints = [
        ('quantity_positive', 'check(quantity > 0)', 'Quantity must be positive!'),
        ('cost_positive', 'check(estimated_cost >= 0)', 'Estimated cost cannot be negative!')
    ]

    @api.constrains('quantity')
    def _check_quantity(self):
        for equipment in self:
            if equipment.quantity < 1:
                raise ValidationError(_('Quantity must be at least 1.'))

    def name_get(self):
        """Custom name display"""
        result = []
        for equipment in self:
            name = f"{equipment.equipment_name}"
            if equipment.equipment_type:
                name = f"[{dict(equipment._fields['equipment_type'].selection)[equipment.equipment_type]}] {name}"
            if equipment.quantity > 1:
                name = f"{name} (x{equipment.quantity})"
            result.append((equipment.id, name))
        return result

    def action_assign_to_employees(self):
        """Open wizard to assign equipment to employees"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Assign Equipment'),
            'res_model': 'hr.job.equipment.assign.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_equipment_id': self.id,
                'default_job_id': self.job_id.id
            }
        }
