# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrApplicantAdditionalInfoWizard(models.TransientModel):
    _name = 'hr.applicant.additional.info.wizard'
    _description = 'Request Additional Information from Candidate'

    applicant_id = fields.Many2one(
        'hr.applicant',
        string='Applicant',
        required=True,
        readonly=True
    )
    
    additional_info_text = fields.Text(
        string='Additional Information Required',
        required=True,
        help="Specify what additional information or documents are needed from the candidate"
    )
    
    send_email = fields.Boolean(
        string='Send Email to Candidate',
        default=True,
        help="Send an email notification to the candidate"
    )

    def action_send_request(self):
        """Send the additional information request"""
        self.ensure_one()
        
        if not self.additional_info_text:
            raise ValidationError(_("Please specify what additional information is required."))
        
        # Call the applicant method to handle the request
        self.applicant_id._send_additional_info_request(self.additional_info_text)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Additional Information Requested'),
                'message': _('Request sent to candidate: %s') % self.applicant_id.partner_name,
                'type': 'success',
                'sticky': False,
            }
        }
