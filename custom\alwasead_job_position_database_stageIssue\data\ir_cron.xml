<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Scheduled Action for Staffing Level Monitoring -->
        <record id="ir_cron_check_staffing_levels" model="ir.cron">
            <field name="name">AlWasead: Check Staffing Levels</field>
            <field name="model_id" ref="hr.model_hr_job"/>
            <field name="state">code</field>
            <field name="code">model._cron_check_staffing_levels()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True"/>
            <field name="nextcall" eval="(DateTime.now().replace(hour=9, minute=0, second=0) + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

    </data>
</odoo>
