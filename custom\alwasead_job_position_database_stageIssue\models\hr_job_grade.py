# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrJobGrade(models.Model):
    _name = 'hr.job.grade'
    _description = 'Job Grade for Experience Level and Salary Calculation'
    _order = 'sequence, name'

    name = fields.Char(string='Grade Name', required=True)
    code = fields.Char(string='Grade Code', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    description = fields.Text(string='Description')

    # Experience requirements
    min_experience_months = fields.Integer(
        string='Minimum Experience (Months)',
        default=0,
        help="Minimum experience in months for this grade"
    )
    max_experience_months = fields.Integer(
        string='Maximum Experience (Months)',
        help="Maximum experience in months for this grade (leave empty for unlimited)"
    )

    # Salary calculation
    base_salary_multiplier = fields.Float(
        string='Base Salary Multiplier',
        default=1.0,
        help='Multiplier applied to base salary for this grade'
    )

    # Contract terms
    default_contract_duration = fields.Integer(
        string='Default Contract Duration (Months)',
        default=12,
        help="Default contract duration for this grade"
    )
    probation_period = fields.Integer(
        string='Probation Period (Months)',
        default=3,
        help="Probation period for this grade"
    )

    # Benefits
    annual_leave_days = fields.Integer(
        string='Annual Leave Days',
        default=21,
        help="Annual leave entitlement for this grade"
    )
    sick_leave_days = fields.Integer(
        string='Sick Leave Days',
        default=14,
        help="Sick leave entitlement for this grade"
    )

    # Related fields
    job_count = fields.Integer(
        string='Number of Jobs',
        compute='_compute_job_count'
    )
    employee_count = fields.Integer(
        string='Number of Employees',
        compute='_compute_employee_count'
    )

    active = fields.Boolean(string='Active', default=True)

    _sql_constraints = [
        ('code_unique', 'unique(code)', 'Grade code must be unique!'),
        ('name_unique', 'unique(name)', 'Grade name must be unique!'),
        ('salary_multiplier_positive', 'check(base_salary_multiplier > 0)', 'Salary multiplier must be positive!')
    ]

    @api.depends('name')
    def _compute_job_count(self):
        for grade in self:
            grade.job_count = self.env['hr.job'].search_count([
                ('job_grade_id', '=', grade.id)
            ])

    @api.depends('name')
    def _compute_employee_count(self):
        for grade in self:
            # Count employees in jobs with this grade
            jobs = self.env['hr.job'].search([('job_grade_id', '=', grade.id)])
            grade.employee_count = sum(job.current_employees for job in jobs)

    def action_view_jobs(self):
        """View all jobs in this grade"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Jobs in Grade: %s') % self.name,
            'res_model': 'hr.job',
            'view_mode': 'tree,form',
            'domain': [('job_grade_id', '=', self.id)],
            'context': {'default_job_grade_id': self.id}
        }

    def action_view_employees(self):
        """View all employees in this grade"""
        jobs = self.env['hr.job'].search([('job_grade_id', '=', self.id)])
        employee_ids = []
        for job in jobs:
            employee_ids.extend(job.employee_ids.ids)

        return {
            'type': 'ir.actions.act_window',
            'name': _('Employees in Grade: %s') % self.name,
            'res_model': 'hr.employee',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', employee_ids)],
        }

    @api.constrains('min_experience_months', 'max_experience_months')
    def _check_experience_months(self):
        for grade in self:
            if grade.min_experience_months < 0:
                raise ValidationError(_('Minimum experience months cannot be negative.'))
            if grade.max_experience_months and grade.max_experience_months < grade.min_experience_months:
                raise ValidationError(_('Maximum experience months cannot be less than minimum experience months.'))

    @api.constrains('default_contract_duration', 'probation_period')
    def _check_contract_terms(self):
        for grade in self:
            if grade.default_contract_duration < 1:
                raise ValidationError(_('Contract duration must be at least 1 month.'))
            if grade.probation_period < 0:
                raise ValidationError(_('Probation period cannot be negative.'))
            if grade.probation_period >= grade.default_contract_duration:
                raise ValidationError(_('Probation period cannot be longer than contract duration.'))

    @api.constrains('annual_leave_days', 'sick_leave_days')
    def _check_leave_days(self):
        for grade in self:
            if grade.annual_leave_days < 0:
                raise ValidationError(_('Annual leave days cannot be negative.'))
            if grade.sick_leave_days < 0:
                raise ValidationError(_('Sick leave days cannot be negative.'))
