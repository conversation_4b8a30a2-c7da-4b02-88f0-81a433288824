<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Staffing Level Notification Email Template -->
        <record id="email_template_staffing_notification" model="mail.template">
            <field name="name">Staffing Level Notification</field>
            <field name="model_id" ref="hr.model_hr_job"/>
            <field name="subject">Staffing Alert: Understaffed Position</field>
            <field name="email_from"><EMAIL></field>
            <field name="email_to">${object.department_id.manager_id.work_email or ''}</field>
            <field name="description">Sent to department manager when job position is understaffed</field>
            <field name="body_html">
                &lt;p&gt;Dear Manager,&lt;/p&gt;
                &lt;p&gt;The job position &lt;strong&gt;${object.name}&lt;/strong&gt; is currently understaffed.&lt;/p&gt;
                &lt;p&gt;Required: ${object.required_positions}&lt;/p&gt;
                &lt;p&gt;Current: ${object.current_employees}&lt;/p&gt;
                &lt;p&gt;Vacant: ${object.vacant_positions}&lt;/p&gt;
                &lt;p&gt;Please consider creating a JVA form.&lt;/p&gt;
                &lt;p&gt;Best regards,&lt;br/&gt;HR System&lt;/p&gt;
            </field>
        </record>
        
    </data>
</odoo>
