<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Division Form View -->
        <record id="view_hr_division_form" model="ir.ui.view">
            <field name="name">hr.division.form</field>
            <field name="model">hr.division</field>
            <field name="arch" type="xml">
                <form string="Division">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_departments" type="object" 
                                    class="oe_stat_button" icon="fa-sitemap">
                                <field name="department_count" widget="statinfo" string="Departments"/>
                            </button>
                            <button name="action_view_employees" type="object" 
                                    class="oe_stat_button" icon="fa-users">
                                <field name="employee_count" widget="statinfo" string="Employees"/>
                            </button>
                            <button name="action_view_jobs" type="object" 
                                    class="oe_stat_button" icon="fa-briefcase">
                                <field name="job_count" widget="statinfo" string="Job Positions"/>
                            </button>
                        </div>
                        
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="code"/>
                                <field name="sequence"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="company_id"/>
                                <field name="head_id"/>
                            </group>
                        </group>
                        
                        <separator string="Description"/>
                        <field name="description" nolabel="1" placeholder="Enter division description..."/>
                        
                        <notebook>
                            <page string="Departments" name="departments">
                                <field name="department_ids" widget="many2many">
                                    <tree>
                                        <field name="name"/>
                                        <field name="head_id"/>
                                        <field name="member_ids" widget="many2many_tags"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Division Tree View -->
        <record id="view_hr_division_tree" model="ir.ui.view">
            <field name="name">hr.division.tree</field>
            <field name="model">hr.division</field>
            <field name="arch" type="xml">
                <tree string="Divisions">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="head_id"/>
                    <field name="department_count"/>
                    <field name="employee_count"/>
                    <field name="job_count"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Unit Form View -->
        <record id="view_hr_unit_form" model="ir.ui.view">
            <field name="name">hr.unit.form</field>
            <field name="model">hr.unit</field>
            <field name="arch" type="xml">
                <form string="Unit/Team">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_employees" type="object"
                                    class="oe_stat_button" icon="fa-users">
                                <field name="employee_count" widget="statinfo" string="Employees"/>
                            </button>
                        </div>

                        <group>
                            <group>
                                <field name="name"/>
                                <field name="code"/>
                                <field name="sequence"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="department_id"/>
                                <field name="division_ids" widget="many2many_tags"/>
                                <field name="head_id"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1"/>
                        </group>

                        <notebook>
                            <page string="Employees" name="employees">
                                <field name="employee_ids" widget="many2many">
                                    <tree>
                                        <field name="name"/>
                                        <field name="job_id"/>
                                        <field name="work_email"/>
                                        <field name="work_phone"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Unit Tree View -->
        <record id="view_hr_unit_tree" model="ir.ui.view">
            <field name="name">hr.unit.tree</field>
            <field name="model">hr.unit</field>
            <field name="arch" type="xml">
                <tree string="Units/Teams">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="department_id"/>
                    <field name="division_ids" widget="many2many_tags"/>
                    <field name="head_id"/>
                    <field name="employee_count"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

    </data>
</odoo>
