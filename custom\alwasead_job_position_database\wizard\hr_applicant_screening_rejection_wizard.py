# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrApplicantScreeningRejectionWizard(models.TransientModel):
    _name = 'hr.applicant.screening.rejection.wizard'
    _description = 'Screening Rejection Wizard'

    applicant_id = fields.Many2one(
        'hr.applicant',
        string='Applicant',
        required=True,
        readonly=True
    )
    
    rejection_reason = fields.Text(
        string='Rejection Reason',
        required=True,
        help="Please provide a detailed reason for rejecting this candidate after screening"
    )

    def action_confirm_rejection(self):
        """Confirm the rejection and update the applicant"""
        self.ensure_one()
        
        if not self.rejection_reason.strip():
            raise ValidationError(_('Please provide a rejection reason.'))
        
        # Call the applicant's rejection method
        self.applicant_id._reject_after_screening(self.rejection_reason)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Candidate has been rejected after screening. HR Recruiter has been notified.'),
                'type': 'success',
                'sticky': False,
            }
        }
