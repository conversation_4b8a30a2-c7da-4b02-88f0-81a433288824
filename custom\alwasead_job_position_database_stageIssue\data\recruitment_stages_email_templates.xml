<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Link Email Templates to Recruitment Stages -->
        <!-- This file is loaded after email templates to avoid dependency issues -->
        
        <!-- Update Sent for Screening Stage with Email Template -->
        <record id="stage_sent_for_screening" model="hr.recruitment.stage">
            <field name="template_id" ref="email_template_staffing_simple"/>
        </record>
        
        <!-- Update Additional Info Requested Stage with Email Template -->
        <record id="stage_additional_info_requested" model="hr.recruitment.stage">
            <field name="template_id" ref="email_template_initial_qualification"/>
        </record>
        
        <!-- Update Rejected at Initial Stage with Email Template -->
        <record id="stage_rejected_initial" model="hr.recruitment.stage">
            <field name="template_id" ref="email_template_application_rejection"/>
        </record>
        
        <!-- Update Initial Qualification Completed Stage with Email Template -->
        <record id="stage_initial_qualification_completed" model="hr.recruitment.stage">
            <field name="template_id" ref="email_template_application_confirmation"/>
        </record>
        
    </data>
</odoo>
