<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Job Grades based on AlWasead requirements -->
        <record id="job_grade_trainee" model="hr.job.grade">
            <field name="name">Trainee</field>
            <field name="code">TRA</field>
            <field name="sequence">10</field>
            <field name="min_experience_months">0</field>
            <field name="max_experience_months">6</field>
            <field name="base_salary_multiplier">0.7</field>
            <field name="default_contract_duration">6</field>
            <field name="probation_period">3</field>
            <field name="annual_leave_days">15</field>
            <field name="sick_leave_days">10</field>
            <field name="description">Entry-level position for new graduates or career changers</field>
        </record>

        <record id="job_grade_intern" model="hr.job.grade">
            <field name="name">Intern</field>
            <field name="code">INT</field>
            <field name="sequence">20</field>
            <field name="min_experience_months">0</field>
            <field name="max_experience_months">12</field>
            <field name="base_salary_multiplier">0.6</field>
            <field name="default_contract_duration">6</field>
            <field name="probation_period">2</field>
            <field name="annual_leave_days">12</field>
            <field name="sick_leave_days">8</field>
            <field name="description">Temporary position for students or recent graduates</field>
        </record>

        <record id="job_grade_junior" model="hr.job.grade">
            <field name="name">Junior</field>
            <field name="code">JUN</field>
            <field name="sequence">30</field>
            <field name="min_experience_months">6</field>
            <field name="max_experience_months">24</field>
            <field name="base_salary_multiplier">0.85</field>
            <field name="default_contract_duration">12</field>
            <field name="probation_period">3</field>
            <field name="annual_leave_days">18</field>
            <field name="sick_leave_days">12</field>
            <field name="description">Entry to mid-level position with basic experience</field>
        </record>

        <record id="job_grade_lead" model="hr.job.grade">
            <field name="name">Lead</field>
            <field name="code">LEA</field>
            <field name="sequence">40</field>
            <field name="min_experience_months">24</field>
            <field name="max_experience_months">60</field>
            <field name="base_salary_multiplier">1.0</field>
            <field name="default_contract_duration">24</field>
            <field name="probation_period">3</field>
            <field name="annual_leave_days">21</field>
            <field name="sick_leave_days">14</field>
            <field name="description">Mid-level position with team leadership responsibilities</field>
        </record>

        <record id="job_grade_senior" model="hr.job.grade">
            <field name="name">Senior</field>
            <field name="code">SEN</field>
            <field name="sequence">50</field>
            <field name="min_experience_months">60</field>
            <field name="max_experience_months">120</field>
            <field name="base_salary_multiplier">1.3</field>
            <field name="default_contract_duration">36</field>
            <field name="probation_period">3</field>
            <field name="annual_leave_days">25</field>
            <field name="sick_leave_days">16</field>
            <field name="description">Senior-level position with extensive experience and expertise</field>
        </record>

        <record id="job_grade_associate_consultant" model="hr.job.grade">
            <field name="name">Associate Consultant</field>
            <field name="code">ASC</field>
            <field name="sequence">60</field>
            <field name="min_experience_months">36</field>
            <field name="max_experience_months">84</field>
            <field name="base_salary_multiplier">1.15</field>
            <field name="default_contract_duration">24</field>
            <field name="probation_period">3</field>
            <field name="annual_leave_days">23</field>
            <field name="sick_leave_days">15</field>
            <field name="description">Consulting position with specialized expertise</field>
        </record>

        <record id="job_grade_lead_consultant" model="hr.job.grade">
            <field name="name">Lead Consultant</field>
            <field name="code">LEC</field>
            <field name="sequence">70</field>
            <field name="min_experience_months">84</field>
            <field name="base_salary_multiplier">1.5</field>
            <field name="default_contract_duration">36</field>
            <field name="probation_period">3</field>
            <field name="annual_leave_days">28</field>
            <field name="sick_leave_days">18</field>
            <field name="description">Senior consulting position with project leadership responsibilities</field>
        </record>

    </data>
</odoo>
