<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Enhanced Applicant Form View -->
        <record id="view_hr_applicant_form_enhanced" model="ir.ui.view">
            <field name="name">hr.applicant.form.enhanced</field>
            <field name="model">hr.applicant</field>
            <field name="inherit_id" ref="hr_recruitment.hr_applicant_view_form"/>
            <field name="arch" type="xml">

                <!-- Add Initial Qualification workflow buttons to header -->
                <xpath expr="//header" position="inside">
                    <!-- HR Recruiter buttons -->
                    <button name="action_send_for_screening"
                            string="Send for Screening"
                            type="object"
                            class="btn-primary"
                            attrs="{'invisible': [('qualification_state', '!=', 'initial')]}"
                            groups="hr_recruitment.group_hr_recruitment_user"/>
                    <button name="action_request_additional_info"
                            string="Request Additional Info"
                            type="object"
                            class="btn-warning"
                            attrs="{'invisible': [('qualification_state', '!=', 'initial')]}"
                            groups="hr_recruitment.group_hr_recruitment_user"/>
                    <button name="action_reject_application"
                            string="Reject Application"
                            type="object"
                            class="btn-danger"
                            attrs="{'invisible': [('qualification_state', '!=', 'initial')]}"
                            groups="hr_recruitment.group_hr_recruitment_user"/>

                    <!-- Technical Interviewer buttons -->
                    <button name="action_accept_for_interview"
                            string="Accept for Interview"
                            type="object"
                            class="btn-success"
                            attrs="{'invisible': [('qualification_state', '!=', 'screening')]}"
                            groups="hr_recruitment.group_hr_recruitment_user"/>
                    <button name="action_reject_after_screening"
                            string="Reject after Screening"
                            type="object"
                            class="btn-danger"
                            attrs="{'invisible': [('qualification_state', '!=', 'screening')]}"
                            groups="hr_recruitment.group_hr_recruitment_user"/>

                    <field name="qualification_state" widget="statusbar" statusbar_visible="initial,screening,additional_info,accepted_for_interview,rejected_after_screening,rejected,completed"/>
                </xpath>

                <!-- Add AlWasead fields after basic information -->
                <xpath expr="//field[@name='partner_mobile']" position="after">
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                </xpath>

                <!-- Add interviewer assignment fields -->
                <xpath expr="//field[@name='user_id']" position="after">
                    <field name="technical_interviewer_id" attrs="{'readonly': [('qualification_state', '!=', 'initial')]}"/>
                    <field name="head_of_department_id" attrs="{'readonly': [('qualification_state', '!=', 'initial')]}"/>
                </xpath>
                
                <!-- Add academic and experience fields -->
                <xpath expr="//field[@name='salary_expected']" position="after">
                    <field name="academic_title"/>
                    <field name="work_experience_years"/>
                    <field name="experience_level" readonly="1"/>
                </xpath>
                
                <!-- Add recruiting source -->
                <xpath expr="//field[@name='source_id']" position="after">
                    <field name="recruiting_source"/>
                </xpath>
                
                <!-- Add document attachments section -->
                <xpath expr="//notebook" position="inside">
                    <page string="Documents &amp; Attachments" name="documents">
                        <group>
                            <group string="Academic Documents">
                                <field name="diploma_attachment_id" widget="binary"/>
                                <button name="action_view_diploma" type="object" 
                                        string="View Diploma" class="btn-link"
                                        attrs="{'invisible': [('diploma_attachment_id', '=', False)]}"/>
                            </group>
                            <group string="Portfolio">
                                <field name="portfolio_attachment_id" widget="binary"/>
                                <button name="action_view_portfolio" type="object" 
                                        string="View Portfolio" class="btn-link"
                                        attrs="{'invisible': [('portfolio_attachment_id', '=', False)]}"/>
                            </group>
                        </group>
                    </page>
                </xpath>
                
                <!-- Add data protection compliance section -->
                <xpath expr="//notebook" position="inside">
                    <page string="Data Protection" name="data_protection">
                        <div class="oe_form_sheet">
                            <group string="Consent Information" class="col-12">
                                <group class="col-6">
                                    <field name="data_protection_consent" readonly="1"/>
                                    <field name="data_protection_consent_date" readonly="1"/>
                                </group>
                            </group>

                            <div class="mt-4">
                                <h3>Data Protection Disclaimer</h3>
                                <div class="alert alert-info" role="alert" style="max-width: none;">
                                    <p class="mb-3">
                                        <strong>By submitting this application, the candidate agrees that AlWasead may collect, process, and store personal data for recruitment purposes only.</strong>
                                    </p>
                                    <p class="mb-2">Your data will be:</p>
                                    <ul class="mb-3">
                                        <li>Processed confidentially and securely according to data protection regulations</li>
                                        <li>Used solely for recruitment and selection purposes</li>
                                        <li>Not shared with third parties without your explicit consent</li>
                                        <li>Retained for a maximum of 2 years after the recruitment process concludes</li>
                                        <li>Subject to your right to request access, correction, or deletion at any time</li>
                                    </ul>
                                    <p class="mb-0">
                                        <strong>Contact Information:</strong> For any data protection inquiries, please contact our HR department at
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </page>
                </xpath>

                <!-- Add Initial Qualification workflow page -->
                <xpath expr="//notebook" position="inside">
                    <page string="Initial Qualification" name="initial_qualification">
                        <div class="oe_form_sheet">
                            <group string="Workflow Status" class="col-12">
                                <group class="col-6">
                                    <field name="qualification_state" readonly="1"/>
                                    <field name="qualification_action_date" readonly="1"/>
                                    <field name="qualification_action_by" readonly="1"/>
                                </group>
                            </group>

                            <group string="Interviewer Assignments" class="col-12">
                                <group class="col-6">
                                    <field name="user_id" string="HR Recruiter" readonly="1"/>
                                    <field name="technical_interviewer_id"/>
                                    <field name="head_of_department_id"/>
                                </group>
                            </group>

                            <div class="mt-4" attrs="{'invisible': [('additional_info_requested', '=', False)]}">
                                <h3>Additional Information Requested</h3>
                                <field name="additional_info_requested" readonly="1" nolabel="1"/>
                            </div>

                            <div class="mt-4" attrs="{'invisible': [('rejection_reason_initial', '=', False)]}">
                                <h3>Rejection Reason</h3>
                                <field name="rejection_reason_initial" readonly="1" nolabel="1"/>
                            </div>
                        </div>
                    </page>
                </xpath>
                
            </field>
        </record>
        
        <!-- Enhanced Applicant Tree View -->
        <record id="view_hr_applicant_tree_enhanced" model="ir.ui.view">
            <field name="name">hr.applicant.tree.enhanced</field>
            <field name="model">hr.applicant</field>
            <field name="inherit_id" ref="hr_recruitment.crm_case_tree_view_job"/>
            <field name="arch" type="xml">

                <!-- Add nationality and experience columns -->
                <xpath expr="//field[@name='partner_mobile']" position="after">
                    <field name="qualification_state" optional="show"/>
                    <field name="nationality_id" optional="hide"/>
                    <field name="work_experience_years" optional="hide"/>
                    <field name="experience_level" optional="hide"/>
                    <field name="recruiting_source" optional="hide"/>
                </xpath>

            </field>
        </record>
        
        <!-- Enhanced Applicant Search View -->
        <record id="view_hr_applicant_search_enhanced" model="ir.ui.view">
            <field name="name">hr.applicant.search.enhanced</field>
            <field name="model">hr.applicant</field>
            <field name="inherit_id" ref="hr_recruitment.hr_applicant_view_search_bis"/>
            <field name="arch" type="xml">
                
                <!-- Add search fields -->
                <xpath expr="//field[@name='refuse_reason_id']" position="after">
                    <field name="qualification_state"/>
                    <field name="nationality_id"/>
                    <field name="country_of_living_id"/>
                    <field name="academic_title"/>
                    <field name="recruiting_source"/>
                </xpath>

                <!-- Add filter groups -->
                <xpath expr="//filter[@name='group_by_categ_ids']" position="after">
                    <filter string="Qualification State" name="group_qualification"
                            context="{'group_by': 'qualification_state'}"/>
                    <filter string="Nationality" name="group_nationality"
                            context="{'group_by': 'nationality_id'}"/>
                    <filter string="Academic Title" name="group_academic"
                            context="{'group_by': 'academic_title'}"/>
                    <filter string="Experience Level" name="group_experience"
                            context="{'group_by': 'experience_level'}"/>
                    <filter string="Recruiting Source" name="group_source"
                            context="{'group_by': 'recruiting_source'}"/>
                </xpath>
                
                <!-- Add filters -->
                <xpath expr="//filter[@name='activities_overdue']" position="after">
                    <separator/>
                    <filter string="Initial Qualification" name="filter_initial"
                            domain="[('qualification_state', '=', 'initial')]"/>
                    <filter string="Sent for Screening" name="filter_screening"
                            domain="[('qualification_state', '=', 'screening')]"/>
                    <filter string="Additional Info Requested" name="filter_additional"
                            domain="[('qualification_state', '=', 'additional_info')]"/>
                    <filter string="Rejected at Initial" name="filter_rejected"
                            domain="[('qualification_state', '=', 'rejected')]"/>
                    <separator/>
                    <filter string="With Diploma" name="filter_diploma"
                            domain="[('diploma_attachment_id', '!=', False)]"/>
                    <filter string="With Portfolio" name="filter_portfolio"
                            domain="[('portfolio_attachment_id', '!=', False)]"/>
                    <filter string="Data Consent Given" name="filter_consent"
                            domain="[('data_protection_consent', '=', True)]"/>
                </xpath>
                
            </field>
        </record>
        
        <!-- Kanban View Enhancement -->
        <record id="view_hr_applicant_kanban_enhanced" model="ir.ui.view">
            <field name="name">hr.applicant.kanban.enhanced</field>
            <field name="model">hr.applicant</field>
            <field name="inherit_id" ref="hr_recruitment.hr_kanban_view_applicant"/>
            <field name="arch" type="xml">

                <!-- Add fields to kanban -->
                <xpath expr="//field[@name='refuse_reason_id']" position="after">
                    <field name="nationality_id"/>
                    <field name="work_experience_years"/>
                    <field name="recruiting_source"/>
                    <field name="diploma_attachment_id"/>
                    <field name="portfolio_attachment_id"/>
                    <field name="data_protection_consent"/>
                </xpath>

                <!-- Add additional fields to kanban -->
                <xpath expr="//field[@name='categ_ids']" position="after">
                    <div t-if="record.nationality_id.raw_value" class="text-muted">
                        <i class="fa fa-globe" title="Nationality"/>
                        <field name="nationality_id"/>
                    </div>
                    <div t-if="record.work_experience_years.raw_value" class="text-muted">
                        <i class="fa fa-briefcase" title="Experience"/>
                        <field name="work_experience_years"/> years
                    </div>
                    <div t-if="record.recruiting_source.raw_value" class="text-muted">
                        <i class="fa fa-share-alt" title="Source"/>
                        <field name="recruiting_source"/>
                    </div>
                </xpath>

                <!-- Add document indicators -->
                <xpath expr="//div[hasclass('oe_kanban_bottom_right')]" position="inside">
                    <span t-if="record.diploma_attachment_id.raw_value"
                          class="badge badge-pill badge-info me-1" title="Has Diploma">
                        <i class="fa fa-graduation-cap"/>
                    </span>
                    <span t-if="record.portfolio_attachment_id.raw_value"
                          class="badge badge-pill badge-success me-1" title="Has Portfolio">
                        <i class="fa fa-folder"/>
                    </span>
                    <span t-if="record.data_protection_consent.raw_value"
                          class="badge badge-pill badge-primary me-1" title="Data Consent Given">
                        <i class="fa fa-shield"/>
                    </span>
                </xpath>
                
            </field>
        </record>
        
    </data>
</odoo>
