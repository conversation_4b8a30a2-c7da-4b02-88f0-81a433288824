2025-07-27 07:48:41,239 133956 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:48:41,247 133956 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:48:41,257 133956 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:48:41,315 133956 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:48:41,387 133956 CRITICAL ? odoo.modules.module: Couldn't load module alwasead_job_position_database 
2025-07-27 07:48:41,387 133956 CRITICAL ? odoo.modules.module: unmatched '}' (hr_applicant.py, line 501) 
2025-07-27 07:48:41,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:48:41,********** ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:41,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:41,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:48:41] "GET /web HTTP/1.1" 500 - 13 0.011 0.152
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:48:53,********** CRITICAL ? odoo.modules.module: Couldn't load module alwasead_job_position_database 
2025-07-27 07:48:53,********** CRITICAL ? odoo.modules.module: unmatched '}' (hr_applicant.py, line 501) 
2025-07-27 07:48:53,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:48:53,********** ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:53,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:53,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:48:53] "GET /websocket HTTP/1.1" 500 - 13 0.018 0.197
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:49:17,********** INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:49:17,********** WARNING ? odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:49:17,********** INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:49:17,********** INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:49:20,704 143168 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 07:49:20,704 143168 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:49:20,704 143168 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:49:20,704 143168 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:49:20,831 143168 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:49:21,021 143168 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:49:21,084 143168 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:49:21,084 143168 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:49:21,116 143168 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:49:21,132 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:49:23,193 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:49:23,201 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:49:24,484 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:49:24,533 143168 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:49:24,553 143168 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:49:26,725 143168 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:49:27,059 143168 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:49:27,296 143168 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:49:27,805 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:49:27,910 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:49:27,917 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:49:27,949 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:49:27,967 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:49:27,979 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:49:27,996 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:49:28,001 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:49:28,012 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:49:28,028 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:49:28,050 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:49:28,054 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:49:28,077 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:49:28,096 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:49:28,100 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:49:28,297 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:49:28,393 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:49:28,428 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:49:28,463 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:49:28,551 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:49:28,613 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:49:28,664 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:49:28,762 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:49:28,933 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:49:28,984 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:49:29,051 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:49:29,068 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:49:29,123 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:49:29,444 143168 WARNING ardano_hr2 odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard', 'hr.applicant.screening.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_screening_rejection_wizard,access_hr_applicant_screening_rejection_wizard,alwasead_job_position_database.model_hr_applicant_screening_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:49:29,466 143168 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.74s, 913 queries (+913 other) 
2025-07-27 07:49:29,466 143168 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 4.93s, 913 queries (+913 extra) 
2025-07-27 07:49:31,279 143168 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 07:49:31,287 143168 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 10.265s 
2025-07-27 07:49:31,443 143168 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:49:35,567 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:35] "GET /web HTTP/1.1" 200 - 178 0.234 14.120
2025-07-27 07:49:36,485 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "GET /websocket HTTP/1.1" 101 - 2 0.014 0.110
2025-07-27 07:49:36,553 143168 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 07:49:36,626 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.196 0.091
2025-07-27 07:49:36,665 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.021 0.085
2025-07-27 07:49:36,707 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.024 0.024
2025-07-27 07:49:36,932 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /web/action/load HTTP/1.1" 200 - 14 0.017 0.625
2025-07-27 07:49:37,201 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.086 0.577
2025-07-27 07:49:37,236 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 66 0.091 0.189
2025-07-27 07:49:37,294 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 8 0.010 0.014
2025-07-27 07:49:37,344 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 7 0.009 0.008
2025-07-27 07:49:37,346 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.009 0.008
2025-07-27 07:49:37,393 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.006 0.009
2025-07-27 07:49:37,427 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.025 0.024
2025-07-27 07:49:37,543 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.003 0.015
2025-07-27 07:49:38,642 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.031 0.025
2025-07-27 07:49:38,679 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.004 0.007
2025-07-27 07:49:38,812 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/data HTTP/1.1" 200 - 32 0.025 0.037
2025-07-27 07:49:38,813 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.035 0.031
2025-07-27 07:49:38,906 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.043 0.017
2025-07-27 07:49:39,275 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.029 0.467
2025-07-27 07:49:39,471 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/759/1920x160 HTTP/1.1" 304 - 8 0.009 0.047
2025-07-27 07:49:39,473 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/760/1920x160 HTTP/1.1" 304 - 8 0.015 0.047
2025-07-27 07:49:39,491 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.008 0.058
2025-07-27 07:49:39,503 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.018 0.058
2025-07-27 07:50:05,626 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.048 0.015
2025-07-27 07:50:05,647 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.056 0.025
2025-07-27 07:50:05,694 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.038 0.049
2025-07-27 07:50:05,871 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:05,871 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:10,276 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /web/dataset/call_button HTTP/1.1" 200 - 53 0.354 4.338
2025-07-27 07:50:10,320 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:10,336 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:10,382 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.041 0.020
2025-07-27 07:50:10,471 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.035 0.021
2025-07-27 07:50:10,477 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.027 0.026
2025-07-27 07:50:10,502 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.072 0.018
2025-07-27 07:50:16,872 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.016
2025-07-27 07:50:16,911 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.043 0.024
2025-07-27 07:50:16,923 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.032 0.031
2025-07-27 07:50:17,021 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /web/dataset/call_button HTTP/1.1" 200 - 35 0.076 0.098
2025-07-27 07:50:17,025 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:17,030 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:17,069 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.015 0.012
2025-07-27 07:50:17,085 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.025 0.021
2025-07-27 07:50:20,550 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.011
2025-07-27 07:50:20,559 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.007 0.017
2025-07-27 07:50:20,584 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.034 0.020
2025-07-27 07:50:20,597 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.023 0.026
2025-07-27 07:50:20,663 143168 INFO ardano_hr2 odoo.addons.base.models.ir_model: Access Denied by ACLs for operation: read, uid: 2, model: hr.applicant.screening.rejection.wizard 
2025-07-27 07:50:20,778 143168 WARNING ardano_hr2 odoo.http: You are not allowed to access 'Screening Rejection Wizard' (hr.applicant.screening.rejection.wizard) records.

No group currently allows this operation.

Contact your administrator to request access if necessary. 
2025-07-27 07:50:20,779 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /web/dataset/call_kw/hr.applicant.screening.rejection.wizard/get_views HTTP/1.1" 200 - 6 0.009 0.123
2025-07-27 07:50:20,825 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.014
2025-07-27 07:50:20,850 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.020 0.025
2025-07-27 07:50:27,935 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.009 0.010
2025-07-27 07:50:27,936 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.011 0.013
2025-07-27 07:50:27,978 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.007 0.008
2025-07-27 07:50:28,002 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:28] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.013 0.028
2025-07-27 07:50:29,483 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 14 0.027 0.021
2025-07-27 07:50:29,524 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.005 0.010
2025-07-27 07:50:29,649 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.025 0.034
2025-07-27 07:50:29,667 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.052 0.033
2025-07-27 07:50:31,996 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:31] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.018 0.019
2025-07-27 07:50:32,032 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:32] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.040 0.033
2025-07-27 07:50:32,079 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:32] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.043 0.057
2025-07-27 07:50:32,117 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:32,117 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:36,420 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /web/dataset/call_button HTTP/1.1" 200 - 35 0.109 4.345
2025-07-27 07:50:36,449 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:36,478 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:36,532 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 14 0.035 0.024
2025-07-27 07:50:36,591 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.023 0.015
2025-07-27 07:50:36,607 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.028 0.023
2025-07-27 07:50:36,613 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.037 0.025
2025-07-27 07:50:38,476 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.008 0.013
2025-07-27 07:50:38,480 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.014 0.020
2025-07-27 07:50:38,509 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.031 0.027
2025-07-27 07:50:38,529 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.027 0.036
2025-07-27 07:50:38,576 143168 INFO ardano_hr2 odoo.addons.base.models.ir_model: Access Denied by ACLs for operation: read, uid: 2, model: hr.applicant.screening.rejection.wizard 
2025-07-27 07:50:38,579 143168 WARNING ardano_hr2 odoo.http: You are not allowed to access 'Screening Rejection Wizard' (hr.applicant.screening.rejection.wizard) records.

No group currently allows this operation.

Contact your administrator to request access if necessary. 
2025-07-27 07:50:38,580 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /web/dataset/call_kw/hr.applicant.screening.rejection.wizard/get_views HTTP/1.1" 200 - 5 0.013 0.009
2025-07-27 07:50:38,601 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.004 0.007
2025-07-27 07:50:38,618 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.014 0.012
2025-07-27 07:51:29,000 149816 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 07:51:29,000 149816 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:51:29,000 149816 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:51:29,000 149816 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:51:29,228 149816 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:51:29,381 149816 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:51:29,445 149816 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:51:29,456 149816 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:51:29,581 149816 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:51:29,586 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:51:32,050 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:51:32,050 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:51:33,704 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:51:33,801 149816 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:51:33,842 149816 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:51:36,145 149816 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:51:36,460 149816 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:51:36,681 149816 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:51:37,236 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:51:37,352 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:51:37,379 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:51:37,414 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:51:37,427 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:51:37,448 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:51:37,459 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:51:37,463 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:51:37,463 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:51:37,500 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:51:37,518 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:51:37,527 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:51:37,567 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:51:37,583 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:51:37,593 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:51:37,781 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:51:37,888 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:51:37,925 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:51:37,953 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:51:38,034 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:51:38,077 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:51:38,109 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:51:38,205 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:51:38,379 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:51:38,428 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:51:38,492 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:51:38,524 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:51:38,594 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:51:38,828 149816 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.68s, 905 queries (+905 other) 
2025-07-27 07:51:38,828 149816 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 5.03s, 905 queries (+905 extra) 
2025-07-27 07:51:40,615 149816 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 07:51:40,631 149816 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 11.254s 
2025-07-27 07:51:41,361 149816 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:51:41,363 149816 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:51:45,428 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:45] "GET /websocket HTTP/1.1" 101 - 8 0.073 15.707
2025-07-27 07:51:45,454 149816 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 07:51:48,646 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:48] "GET /web HTTP/1.1" 200 - 176 0.164 19.068
2025-07-27 07:51:49,412 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:49] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.014 0.019
2025-07-27 07:51:49,518 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:49] "POST /mail/init_messaging HTTP/1.1" 200 - 55 0.157 0.034
2025-07-27 07:51:49,566 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:49] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.013 0.019
2025-07-27 07:51:49,757 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:49] "POST /web/action/load HTTP/1.1" 200 - 14 0.000 0.457
2025-07-27 07:51:50,027 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 66 0.090 0.164
2025-07-27 07:51:50,075 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.105 0.608
2025-07-27 07:51:50,075 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 8 0.000 0.015
2025-07-27 07:51:50,142 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 15 0.033 0.013
2025-07-27 07:51:50,187 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 6 0.016 0.000
2025-07-27 07:51:50,426 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /mail/thread/messages HTTP/1.1" 200 - 34 0.058 0.063
2025-07-27 07:51:50,506 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /mail/thread/data HTTP/1.1" 200 - 26 0.078 0.123
2025-07-27 07:51:50,506 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /mail/thread/data HTTP/1.1" 200 - 26 0.070 0.113
2025-07-27 07:51:50,870 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.028 0.537
2025-07-27 07:51:50,967 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 200 - 8 0.002 0.022
2025-07-27 07:51:50,979 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 200 - 8 0.011 0.025
2025-07-27 07:51:50,983 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.019 0.009
2025-07-27 07:53:12,771 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:12] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.025 0.012
2025-07-27 07:53:12,785 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:12] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.025 0.023
2025-07-27 07:53:12,785 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:12] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.025 0.014
2025-07-27 07:53:13,000 149816 INFO ardano_hr2 odoo.addons.phone_validation.tools.phone_validation: The `phonenumbers` Python module is not installed, contact numbers will not be verified. Please install the `phonenumbers` Python module. 
2025-07-27 07:53:13,012 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_button HTTP/1.1" 200 - 57 0.163 0.106
2025-07-27 07:53:13,155 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/calendar.event/get_views HTTP/1.1" 200 - 43 0.037 0.077
2025-07-27 07:53:13,217 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/calendar.event/check_access_rights HTTP/1.1" 200 - 3 0.005 0.010
2025-07-27 07:53:13,221 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.004 0.014
2025-07-27 07:53:13,243 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /appointment/appointment_type/get_staff_user_appointment_types HTTP/1.1" 200 - 6 0.027 0.010
2025-07-27 07:53:13,268 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/calendar.filters/search_read HTTP/1.1" 200 - 7 0.021 0.007
2025-07-27 07:53:13,307 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/calendar.event/search_read HTTP/1.1" 200 - 4 0.013 0.009
2025-07-27 07:53:13,334 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/res.partner/get_attendee_detail HTTP/1.1" 200 - 3 0.002 0.007
2025-07-27 07:53:13,482 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/core/main.css HTTP/1.1" 200 - 0 0.000 0.050
2025-07-27 07:53:13,489 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/daygrid/main.css HTTP/1.1" 200 - 0 0.000 0.052
2025-07-27 07:53:13,490 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/timegrid/main.css HTTP/1.1" 200 - 0 0.000 0.049
2025-07-27 07:53:13,492 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/list/main.css HTTP/1.1" 200 - 0 0.000 0.040
2025-07-27 07:53:13,558 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /onboarding/appointment HTTP/1.1" 200 - 29 0.093 0.101
2025-07-27 07:53:13,905 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/core/main.js HTTP/1.1" 200 - 0 0.000 0.481
2025-07-27 07:53:14,081 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/interaction/main.js HTTP/1.1" 200 - 0 0.000 0.145
2025-07-27 07:53:14,231 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/daygrid/main.js HTTP/1.1" 200 - 0 0.000 0.127
2025-07-27 07:53:14,292 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/luxon/main.js HTTP/1.1" 200 - 0 0.000 0.041
2025-07-27 07:53:14,411 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/timegrid/main.js HTTP/1.1" 200 - 0 0.000 0.102
2025-07-27 07:53:14,494 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/list/main.js HTTP/1.1" 200 - 0 0.000 0.062
2025-07-27 07:53:14,576 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/image/onboarding.onboarding/1/panel_background_image HTTP/1.1" 200 - 7 0.010 0.019
2025-07-27 07:53:14,689 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/image/res.partner/9/avatar_128 HTTP/1.1" 200 - 7 0.012 0.041
2025-07-27 07:53:14,695 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/image/res.partner/7/avatar_128 HTTP/1.1" 304 - 8 0.016 0.025
2025-07-27 07:53:16,174 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.004 0.010
2025-07-27 07:53:16,286 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 15 0.054 0.023
2025-07-27 07:53:16,324 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.004 0.009
2025-07-27 07:53:16,430 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.036 0.016
2025-07-27 07:53:16,431 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /mail/thread/data HTTP/1.1" 200 - 14 0.022 0.027
2025-07-27 07:53:17,742 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:17] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.008 0.007
2025-07-27 07:53:17,748 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:17] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.013 0.011
2025-07-27 07:53:17,782 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:17] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.006 0.008
2025-07-27 07:53:17,833 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:17] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.039 0.027
2025-07-27 07:53:24,467 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/action/load HTTP/1.1" 200 - 14 0.044 0.025
2025-07-27 07:53:24,695 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 52 0.091 0.119
2025-07-27 07:53:24,753 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.013 0.012
2025-07-27 07:53:24,884 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 32 0.069 0.084
2025-07-27 07:53:24,920 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.004 0.008
2025-07-27 07:53:25,141 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753602804956 HTTP/1.1" 200 - 11 0.028 0.079
2025-07-27 07:53:25,142 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753602804954 HTTP/1.1" 200 - 11 0.033 0.078
2025-07-27 07:53:25,143 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753602804956 HTTP/1.1" 200 - 11 0.038 0.072
2025-07-27 07:53:25,189 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753602804957 HTTP/1.1" 200 - 6 0.020 0.132
2025-07-27 07:53:25,204 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753602804956 HTTP/1.1" 200 - 6 0.040 0.122
2025-07-27 07:53:25,205 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753602804957 HTTP/1.1" 200 - 6 0.031 0.129
2025-07-27 07:53:25,274 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753602804957 HTTP/1.1" 200 - 6 0.035 0.073
2025-07-27 07:53:25,276 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.036 0.059
2025-07-27 07:53:25,284 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.042 0.049
2025-07-27 07:53:25,303 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.029 0.043
2025-07-27 07:53:25,309 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.048 0.047
2025-07-27 07:53:25,318 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.022 0.051
2025-07-27 07:53:25,331 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.013 0.034
2025-07-27 07:53:25,338 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753602804959 HTTP/1.1" 200 - 6 0.016 0.029
2025-07-27 07:53:25,348 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753602804959 HTTP/1.1" 200 - 6 0.017 0.034
2025-07-27 07:53:25,369 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.010 0.031
2025-07-27 07:53:25,371 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.016 0.038
2025-07-27 07:53:25,377 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.011 0.024
2025-07-27 07:53:25,379 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.011 0.025
2025-07-27 07:53:25,384 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.011 0.022
2025-07-27 07:53:32,461 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:32] "POST /web/action/load HTTP/1.1" 200 - 13 0.050 0.017
2025-07-27 07:53:32,516 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:32] "POST /web/dataset/call_kw/hr.job.category/get_views HTTP/1.1" 200 - 16 0.024 0.016
2025-07-27 07:53:32,550 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:32] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 4 0.004 0.007
2025-07-27 07:53:32,561 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:32] "POST /web/dataset/call_kw/hr.job.category/web_search_read HTTP/1.1" 200 - 6 0.013 0.010
2025-07-27 07:53:34,384 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 4 0.002 0.032
2025-07-27 07:53:34,447 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.010 0.014
2025-07-27 07:53:34,529 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 24 0.042 0.061
2025-07-27 07:53:34,569 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.005 0.008
2025-07-27 07:53:34,984 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753602814594 HTTP/1.1" 200 - 6 0.022 0.102
2025-07-27 07:53:34,985 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753602814597 HTTP/1.1" 200 - 6 0.020 0.100
2025-07-27 07:53:35,012 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753602814597 HTTP/1.1" 200 - 6 0.018 0.118
2025-07-27 07:53:35,017 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753602814598 HTTP/1.1" 200 - 6 0.019 0.113
2025-07-27 07:53:35,019 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753602814598 HTTP/1.1" 200 - 6 0.018 0.104
2025-07-27 07:53:35,023 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753602814596 HTTP/1.1" 200 - 6 0.032 0.118
2025-07-27 07:53:35,040 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753602814599 HTTP/1.1" 200 - 6 0.015 0.028
2025-07-27 07:53:35,043 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753602814598 HTTP/1.1" 200 - 6 0.012 0.040
2025-07-27 07:53:35,070 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753602814600 HTTP/1.1" 200 - 6 0.011 0.037
2025-07-27 07:53:35,079 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753602814600 HTTP/1.1" 200 - 6 0.016 0.033
2025-07-27 07:53:35,085 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753602814601 HTTP/1.1" 200 - 6 0.016 0.032
2025-07-27 07:53:35,088 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753602814601 HTTP/1.1" 200 - 6 0.018 0.027
2025-07-27 07:53:35,095 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753602814601 HTTP/1.1" 200 - 6 0.011 0.034
2025-07-27 07:53:35,096 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753602814602 HTTP/1.1" 200 - 6 0.020 0.022
2025-07-27 07:53:35,118 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753602814602 HTTP/1.1" 200 - 6 0.013 0.029
2025-07-27 07:53:35,133 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753602814602 HTTP/1.1" 200 - 6 0.016 0.026
2025-07-27 07:53:35,145 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753602814603 HTTP/1.1" 200 - 6 0.012 0.031
2025-07-27 07:53:35,146 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753602814603 HTTP/1.1" 200 - 6 0.011 0.024
2025-07-27 07:53:35,155 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753602814603 HTTP/1.1" 200 - 6 0.012 0.026
2025-07-27 07:53:35,155 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753602814603 HTTP/1.1" 200 - 6 0.013 0.037
2025-07-27 07:53:36,587 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:36] "POST /web/action/load HTTP/1.1" 200 - 12 0.027 0.023
2025-07-27 07:53:36,695 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:36] "POST /web/dataset/call_kw/hr.job.grade/get_views HTTP/1.1" 200 - 14 0.043 0.039
2025-07-27 07:53:36,798 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:36] "POST /web/dataset/call_kw/hr.job.grade/web_search_read HTTP/1.1" 200 - 21 0.048 0.017
2025-07-27 07:53:40,723 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:40] "POST /web/action/load HTTP/1.1" 200 - 12 0.013 0.017
2025-07-27 07:53:40,804 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:40] "POST /web/dataset/call_kw/hr.division/get_views HTTP/1.1" 200 - 17 0.021 0.039
2025-07-27 07:53:40,897 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:40] "POST /web/dataset/call_kw/hr.division/web_search_read HTTP/1.1" 200 - 16 0.035 0.023
2025-07-27 07:53:44,780 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:44] "POST /web/action/load HTTP/1.1" 200 - 12 0.009 0.020
2025-07-27 07:53:44,848 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:44] "POST /web/dataset/call_kw/hr.job.equipment/get_views HTTP/1.1" 200 - 17 0.015 0.030
2025-07-27 07:53:44,891 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:44] "POST /web/dataset/call_kw/hr.job.equipment/web_search_read HTTP/1.1" 200 - 5 0.006 0.008
2025-07-27 07:53:46,951 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:46] "POST /web/action/load HTTP/1.1" 200 - 12 0.011 0.015
2025-07-27 07:53:47,004 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:47] "POST /web/dataset/call_kw/hr.unit/get_views HTTP/1.1" 200 - 14 0.010 0.026
2025-07-27 07:53:47,074 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:47] "POST /web/dataset/call_kw/hr.unit/web_search_read HTTP/1.1" 200 - 12 0.036 0.011
2025-07-27 07:53:47,100 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:47] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.006 0.005
2025-07-27 07:53:51,861 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:51] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.007 0.017
2025-07-27 07:53:52,005 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 24 0.048 0.085
2025-07-27 07:53:52,110 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.004 0.014
2025-07-27 07:53:52,347 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753602832145 HTTP/1.1" 200 - 6 0.005 0.025
2025-07-27 07:53:52,390 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753602832150 HTTP/1.1" 200 - 6 0.014 0.020
2025-07-27 07:53:52,401 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753602832147 HTTP/1.1" 200 - 6 0.005 0.018
2025-07-27 07:53:52,416 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753602832148 HTTP/1.1" 200 - 6 0.002 0.021
2025-07-27 07:53:52,439 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753602832151 HTTP/1.1" 200 - 6 0.009 0.017
2025-07-27 07:53:52,452 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753602832152 HTTP/1.1" 200 - 6 0.007 0.018
2025-07-27 07:53:52,479 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753602832153 HTTP/1.1" 200 - 6 0.006 0.020
2025-07-27 07:53:52,491 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753602832153 HTTP/1.1" 200 - 6 0.006 0.018
2025-07-27 07:53:52,518 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753602832154 HTTP/1.1" 200 - 6 0.004 0.019
2025-07-27 07:53:52,539 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753602832155 HTTP/1.1" 200 - 6 0.006 0.021
2025-07-27 07:53:52,553 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753602832156 HTTP/1.1" 200 - 6 0.005 0.015
2025-07-27 07:53:52,574 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753602832157 HTTP/1.1" 200 - 6 0.006 0.014
2025-07-27 07:53:52,597 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753602832157 HTTP/1.1" 200 - 6 0.009 0.012
2025-07-27 07:53:52,612 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753602832158 HTTP/1.1" 200 - 6 0.006 0.016
2025-07-27 07:53:52,636 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753602832158 HTTP/1.1" 200 - 6 0.006 0.019
2025-07-27 07:53:52,644 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753602832159 HTTP/1.1" 200 - 6 0.010 0.011
2025-07-27 07:53:52,659 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753602832159 HTTP/1.1" 200 - 6 0.006 0.018
2025-07-27 07:53:52,670 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753602832160 HTTP/1.1" 200 - 6 0.006 0.014
2025-07-27 07:53:52,684 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753602832160 HTTP/1.1" 200 - 6 0.007 0.019
2025-07-27 07:53:52,688 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753602832160 HTTP/1.1" 200 - 6 0.006 0.012
2025-07-27 07:53:53,697 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 33 0.050 0.027
2025-07-27 07:53:53,720 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.003 0.004
2025-07-27 07:53:53,749 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.007 0.008
2025-07-27 07:53:53,759 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 8 0.013 0.013
2025-07-27 07:53:53,849 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /hr/get_org_chart HTTP/1.1" 200 - 10 0.016 0.016
2025-07-27 07:53:53,864 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /mail/thread/data HTTP/1.1" 200 - 17 0.023 0.020
2025-07-27 07:53:53,884 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.046 0.018
2025-07-27 07:53:53,916 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "GET /web/image?model=hr.employee&id=17&field=avatar_128&unique=1753543833000 HTTP/1.1" 200 - 6 0.005 0.008
2025-07-27 07:53:55,941 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:55] "POST /web/dataset/call_kw/hr.unit/name_search HTTP/1.1" 200 - 4 0.006 0.010
2025-07-27 07:53:59,042 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:59] "POST /web/dataset/call_kw/hr.job/name_search HTTP/1.1" 200 - 5 0.004 0.010
2025-07-27 07:54:02,317 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:02] "POST /web/dataset/call_kw/hr.employee/onchange HTTP/1.1" 200 - 9 0.015 0.021
2025-07-27 07:54:02,379 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:02] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.019 0.017
2025-07-27 07:54:02,381 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:02] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.032 0.009
2025-07-27 07:54:05,589 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:05] "POST /web/dataset/call_kw/hr.unit/name_search HTTP/1.1" 200 - 4 0.006 0.008
2025-07-27 07:54:06,415 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:06] "POST /web/dataset/call_kw/hr.department/name_search HTTP/1.1" 200 - 5 0.005 0.006
2025-07-27 07:54:08,085 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:08] "POST /web/dataset/call_kw/hr.employee/onchange HTTP/1.1" 200 - 7 0.010 0.016
2025-07-27 07:54:08,159 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:08] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.011 0.019
2025-07-27 07:54:08,172 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:08] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.032 0.014
2025-07-27 07:54:09,127 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:09] "POST /web/dataset/call_kw/hr.unit/name_search HTTP/1.1" 200 - 5 0.008 0.009
2025-07-27 07:54:09,745 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:09] "POST /web/dataset/call_kw/hr.unit/read HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 07:54:09,823 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:09] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.034 0.012
2025-07-27 07:54:09,841 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:09] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.041 0.020
2025-07-27 07:54:13,276 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:13] "POST /web/dataset/call_kw/hr.job/name_search HTTP/1.1" 200 - 5 0.006 0.012
2025-07-27 07:54:14,496 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:14] "POST /web/dataset/call_kw/hr.employee/onchange HTTP/1.1" 200 - 14 0.017 0.025
2025-07-27 07:54:14,545 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:14] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.013
2025-07-27 07:54:14,566 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:14] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.020 0.019
2025-07-27 07:54:14,569 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:14] "POST /hr/get_org_chart HTTP/1.1" 200 - 6 0.009 0.020
2025-07-27 07:54:17,629 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:17] "POST /web/dataset/call_kw/hr.department/name_search HTTP/1.1" 200 - 5 0.004 0.006
2025-07-27 07:54:18,383 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:18] "POST /web/dataset/call_kw/hr.employee/onchange HTTP/1.1" 200 - 7 0.008 0.016
2025-07-27 07:54:18,436 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.017
2025-07-27 07:54:18,452 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:18] "POST /hr/get_org_chart HTTP/1.1" 200 - 6 0.010 0.018
2025-07-27 07:54:18,463 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:18] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.015 0.029
2025-07-27 07:54:19,379 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:19] "POST /web/dataset/call_kw/hr.unit/name_search HTTP/1.1" 200 - 5 0.009 0.009
2025-07-27 07:54:19,965 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:19] "POST /web/dataset/call_kw/hr.unit/read HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 07:54:20,012 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.006 0.017
2025-07-27 07:54:20,031 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:20] "POST /hr/get_org_chart HTTP/1.1" 200 - 6 0.016 0.014
2025-07-27 07:54:20,038 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:20] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.020 0.024
2025-07-27 07:54:32,477 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.016
2025-07-27 07:54:32,509 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.036 0.022
2025-07-27 07:54:32,603 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/hr.employee/write HTTP/1.1" 200 - 33 0.097 0.051
2025-07-27 07:54:32,701 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 35 0.050 0.038
2025-07-27 07:54:32,737 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/hr.unit/read HTTP/1.1" 200 - 4 0.004 0.006
2025-07-27 07:54:32,738 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.003 0.010
2025-07-27 07:54:32,769 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 5 0.005 0.009
2025-07-27 07:54:32,776 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.012 0.010
2025-07-27 07:54:32,846 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.035 0.019
2025-07-27 07:54:32,861 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /hr/get_org_chart HTTP/1.1" 200 - 10 0.030 0.022
2025-07-27 07:54:32,862 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.047 0.019
2025-07-27 07:54:32,863 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/messages HTTP/1.1" 200 - 23 0.051 0.022
2025-07-27 07:54:32,875 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "GET /web/image?model=hr.employee&id=17&field=avatar_128&unique=1753602872000 HTTP/1.1" 200 - 6 0.010 0.024
2025-07-27 07:54:32,924 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "GET /web/image/hr.employee.public/17/avatar_1024/ HTTP/1.1" 200 - 11 0.012 0.015
2025-07-27 07:54:32,928 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "GET /web/image/hr.employee.public/1/avatar_1024/ HTTP/1.1" 200 - 11 0.014 0.016
2025-07-27 07:54:34,009 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 24 0.025 0.056
2025-07-27 07:54:34,039 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.002 0.006
2025-07-27 07:54:34,258 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753602874056 HTTP/1.1" 200 - 6 0.024 0.028
2025-07-27 07:54:34,283 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753602874057 HTTP/1.1" 200 - 6 0.019 0.053
2025-07-27 07:54:34,305 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753602874058 HTTP/1.1" 200 - 6 0.020 0.057
2025-07-27 07:54:34,309 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753602874057 HTTP/1.1" 200 - 6 0.030 0.067
2025-07-27 07:54:34,320 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753602874058 HTTP/1.1" 200 - 6 0.027 0.072
2025-07-27 07:54:34,356 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753602874057 HTTP/1.1" 200 - 6 0.037 0.080
2025-07-27 07:54:34,362 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753602874058 HTTP/1.1" 200 - 6 0.024 0.064
2025-07-27 07:54:34,394 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753602874059 HTTP/1.1" 200 - 6 0.023 0.069
2025-07-27 07:54:34,411 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753602874059 HTTP/1.1" 200 - 6 0.024 0.060
2025-07-27 07:54:34,418 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753602874059 HTTP/1.1" 200 - 6 0.017 0.059
2025-07-27 07:54:34,435 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753602874059 HTTP/1.1" 200 - 6 0.028 0.059
2025-07-27 07:54:34,451 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753602874061 HTTP/1.1" 200 - 6 0.024 0.061
2025-07-27 07:54:34,471 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753602874061 HTTP/1.1" 200 - 6 0.034 0.059
2025-07-27 07:54:34,490 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753602874062 HTTP/1.1" 200 - 6 0.026 0.053
2025-07-27 07:54:34,500 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753602874062 HTTP/1.1" 200 - 6 0.024 0.037
2025-07-27 07:54:34,501 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753602874062 HTTP/1.1" 200 - 6 0.020 0.036
2025-07-27 07:54:34,519 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753602874064 HTTP/1.1" 200 - 6 0.012 0.030
2025-07-27 07:54:34,520 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753602874064 HTTP/1.1" 200 - 6 0.016 0.040
2025-07-27 07:54:34,525 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753602874064 HTTP/1.1" 200 - 6 0.014 0.029
2025-07-27 07:54:34,528 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753602874065 HTTP/1.1" 200 - 6 0.011 0.020
2025-07-27 07:54:37,181 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:37] "POST /web/action/load HTTP/1.1" 200 - 17 0.039 0.035
2025-07-27 07:54:37,340 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:37] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 45 0.043 0.089
2025-07-27 07:54:37,391 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:37] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.014 0.014
2025-07-27 07:54:37,425 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:37] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.025 0.035
2025-07-27 07:54:51,083 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 37 0.076 0.037
2025-07-27 07:54:51,113 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.003 0.008
2025-07-27 07:54:51,147 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.008 0.011
2025-07-27 07:54:51,248 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.022 0.017
2025-07-27 07:54:51,294 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /mail/thread/messages HTTP/1.1" 200 - 32 0.054 0.033
2025-07-27 08:04:05,335 145112 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 08:04:05,337 145112 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 08:04:05,338 145112 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 08:04:05,338 145112 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 08:04:05,581 145112 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 08:04:05,799 145112 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 08:04:05,868 145112 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 08:04:05,877 145112 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 08:04:05,938 145112 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 08:04:05,945 145112 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 08:04:08,755 145112 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 08:04:08,755 145112 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 08:04:10,698 145112 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 08:04:10,792 145112 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 08:04:10,841 145112 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 08:04:13,214 145112 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 08:04:13,536 145112 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 08:04:13,738 145112 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 08:04:14,248 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 08:04:14,360 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 08:04:14,378 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 08:04:14,391 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 08:04:14,410 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 08:04:14,423 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 08:04:14,423 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 08:04:14,439 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 08:04:14,439 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 08:04:14,455 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 08:04:14,471 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 08:04:14,487 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 08:04:14,519 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 08:04:14,519 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 08:04:14,535 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 08:04:14,726 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 08:04:14,836 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 08:04:14,869 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 08:04:14,906 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 08:04:14,968 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 08:04:15,004 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 08:04:15,035 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 08:04:15,123 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 08:04:15,275 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 08:04:15,315 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 08:04:15,363 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 08:04:15,379 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 08:04:15,442 145112 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 08:04:15,697 145112 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.48s, 908 queries (+908 other) 
2025-07-27 08:04:15,707 145112 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 4.91s, 908 queries (+908 extra) 
2025-07-27 08:04:17,404 145112 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 08:04:17,415 145112 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 11.623s 
2025-07-27 08:04:17,594 145112 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 08:04:17,597 145112 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 08:04:19,679 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:19] "GET /websocket HTTP/1.1" 101 - 8 0.040 13.700
2025-07-27 08:04:19,727 145112 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 08:04:22,740 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:22] "GET /web HTTP/1.1" 200 - 176 0.282 13.361
2025-07-27 08:04:23,201 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:23] "GET /web/webclient/load_menus/149d0b4aac7e762454ea7c4094617daa0453bf6c240f1ef33ca5a02db7f706c5 HTTP/1.1" 200 - 6 0.011 0.207
2025-07-27 08:04:23,950 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:23] "POST /web/action/load HTTP/1.1" 200 - 18 0.030 0.035
2025-07-27 08:04:24,013 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.013 0.010
2025-07-27 08:04:24,208 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 58 0.086 0.132
2025-07-27 08:04:24,265 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.109 0.088
2025-07-27 08:04:24,265 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /mail/init_messaging HTTP/1.1" 200 - 55 0.124 0.075
2025-07-27 08:04:24,342 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /mail/load_message_failures HTTP/1.1" 200 - 12 0.032 0.001
2025-07-27 08:04:24,409 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 43 0.119 0.025
2025-07-27 08:04:24,464 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.011 0.001
2025-07-27 08:04:24,498 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 8 0.016 0.000
2025-07-27 08:04:24,610 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /mail/thread/data HTTP/1.1" 200 - 23 0.027 0.021
2025-07-27 08:04:24,661 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:24] "POST /mail/thread/messages HTTP/1.1" 200 - 30 0.075 0.023
2025-07-27 08:04:25,042 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:25] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.043 0.423
2025-07-27 08:04:25,159 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:25] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.014 0.017
2025-07-27 08:04:25,159 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:25] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.015 0.012
2025-07-27 08:04:25,159 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:25] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.027 0.003
2025-07-27 08:04:37,757 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:37] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.010 0.010
2025-07-27 08:04:37,777 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:37] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.023 0.015
2025-07-27 08:04:37,793 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:37] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.019 0.017
2025-07-27 08:04:37,964 145112 ERROR ardano_hr2 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1889, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\dataset.py", line 46, in call_button
    action = self._call_kw(model, method, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\dataset.py", line 33, in _call_kw
    return call_kw(request.env[model], method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 468, in call_kw
    result = _call_kw_multi(method, model, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 453, in _call_kw_multi
    result = method(recs, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_job.py", line 294, in action_submit_for_dept_approval
    self.write({
  File "C:\odoo16\server\odoo\addons\hr_recruitment\models\hr_job.py", line 246, in write
    res = super().write(vals)
          ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail\models\mail_alias_mixin.py", line 69, in write
    super(AliasMixin, self).write(record_vals)
  File "C:\odoo16\server\odoo\addons\hr\models\hr_job.py", line 62, in write
    return super(Job, self).write(vals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail\models\mail_thread.py", line 315, in write
    result = super(MailThread, self).write(values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\mixins.py", line 221, in write
    return super(WebsitePublishedMixin, self).write(values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 3737, in write
    raise ValueError("Invalid field %r on model %r" % (fname, self._name))
ValueError: Invalid field 'dept_approval_date' on model 'hr.job'
2025-07-27 08:04:37,966 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:37] "POST /web/dataset/call_button HTTP/1.1" 200 - 13 0.028 0.192
2025-07-27 08:04:37,998 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:37] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.004 0.012
2025-07-27 08:04:38,010 145112 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:04:38] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.014 0.014
2025-07-27 08:05:33,311 115672 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 08:05:33,311 115672 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 08:05:33,311 115672 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 08:05:33,311 115672 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 08:05:33,548 115672 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 08:05:33,765 115672 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 08:05:33,823 115672 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 08:05:33,829 115672 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 08:05:33,890 115672 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 08:05:33,896 115672 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 08:05:36,553 115672 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 08:05:36,553 115672 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 08:05:38,518 115672 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 08:05:38,604 115672 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 08:05:38,656 115672 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 08:05:41,290 115672 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 08:05:41,651 115672 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 08:05:41,********** INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 08:05:42,602 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 08:05:42,725 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 08:05:42,749 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 08:05:42,775 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 08:05:42,795 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 08:05:42,809 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 08:05:42,820 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 08:05:42,828 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 08:05:42,837 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 08:05:42,859 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 08:05:42,873 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 08:05:42,883 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 08:05:42,913 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 08:05:42,922 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 08:05:42,931 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 08:05:43,123 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 08:05:43,230 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 08:05:43,280 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 08:05:43,327 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 08:05:43,422 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 08:05:43,464 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 08:05:43,510 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 08:05:43,********** INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 08:05:43,784 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 08:05:43,838 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 08:05:43,895 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 08:05:43,916 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 08:05:43,991 115672 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 08:05:44,268 115672 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.98s, 917 queries (+917 other) 
2025-07-27 08:05:44,268 115672 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 5.66s, 917 queries (+917 extra) 
2025-07-27 08:05:46,087 115672 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 08:05:46,100 115672 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 12.338s 
2025-07-27 08:05:46,262 115672 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 08:05:46,262 115672 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 08:05:48,434 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:48] "GET /websocket HTTP/1.1" 101 - 8 0.056 14.376
2025-07-27 08:05:48,575 115672 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 08:05:51,437 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:51] "GET /web HTTP/1.1" 200 - 177 0.210 17.335
2025-07-27 08:05:52,621 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:52] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.021 0.021
2025-07-27 08:05:52,646 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:52] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.155 0.043
2025-07-27 08:05:52,700 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:52] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.017 0.019
2025-07-27 08:05:52,903 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:52] "POST /web/action/load HTTP/1.1" 200 - 18 0.041 0.535
2025-07-27 08:05:53,132 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:53] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.072 0.530
2025-07-27 08:05:53,134 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:53] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 59 0.074 0.139
2025-07-27 08:05:53,294 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:53] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 43 0.080 0.037
2025-07-27 08:05:53,344 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:53] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.003 0.008
2025-07-27 08:05:53,396 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:53] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 8 0.015 0.009
2025-07-27 08:05:53,528 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:53] "POST /mail/thread/data HTTP/1.1" 200 - 23 0.046 0.022
2025-07-27 08:05:53,552 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:53] "POST /mail/thread/messages HTTP/1.1" 200 - 34 0.056 0.038
2025-07-27 08:05:53,970 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:53] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.031 0.470
2025-07-27 08:05:54,090 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:54] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.009 0.014
2025-07-27 08:05:54,096 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:54] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.013 0.014
2025-07-27 08:05:54,109 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:54] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.023 0.016
2025-07-27 08:05:55,984 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:55] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.036 0.115
2025-07-27 08:05:55,986 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:55] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.054 0.103
2025-07-27 08:05:56,014 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:56] "POST /web/dataset/call_button HTTP/1.1" 200 - 23 0.099 0.073
2025-07-27 08:05:56,029 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:56] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.026 0.149
2025-07-27 08:05:56,127 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:56] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 33 0.056 0.031
2025-07-27 08:05:56,156 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:56] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.004 0.006
2025-07-27 08:05:56,187 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:56] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.009 0.007
2025-07-27 08:05:56,280 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:56] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.019 0.020
2025-07-27 08:05:56,291 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:56] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.035 0.019
2025-07-27 08:05:56,313 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:56] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.060 0.019
2025-07-27 08:05:59,279 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.018 0.025
2025-07-27 08:05:59,279 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.036 0.012
2025-07-27 08:05:59,305 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.034 0.025
2025-07-27 08:05:59,315 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.045 0.030
2025-07-27 08:05:59,424 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 33 0.064 0.029
2025-07-27 08:05:59,452 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.004 0.008
2025-07-27 08:05:59,492 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.013 0.011
2025-07-27 08:05:59,571 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.029 0.021
2025-07-27 08:05:59,572 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.028 0.020
2025-07-27 08:05:59,580 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:05:59] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.041 0.021
2025-07-27 08:06:01,972 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:01] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.009
2025-07-27 08:06:01,992 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:01] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.017 0.018
2025-07-27 08:06:02,017 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:02] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.020 0.020
2025-07-27 08:06:02,053 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:02] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.053 0.032
2025-07-27 08:06:02,133 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:02] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 33 0.029 0.033
2025-07-27 08:06:02,163 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:02] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.003 0.005
2025-07-27 08:06:02,191 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:02] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.005 0.009
2025-07-27 08:06:02,251 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:02] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.016 0.018
2025-07-27 08:06:02,262 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:02] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.016
2025-07-27 08:06:02,285 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:02] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.050 0.021
2025-07-27 08:06:04,901 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:04] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.005 0.010
2025-07-27 08:06:04,917 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:04] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.017 0.013
2025-07-27 08:06:04,939 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:04] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.016 0.017
2025-07-27 08:06:04,959 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:04] "POST /web/dataset/call_button HTTP/1.1" 200 - 14 0.036 0.022
2025-07-27 08:06:05,035 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:05] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 32 0.029 0.029
2025-07-27 08:06:05,060 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:05] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 08:06:05,087 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:05] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.004 0.009
2025-07-27 08:06:05,141 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:05] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.022 0.017
2025-07-27 08:06:05,144 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:05] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.014
2025-07-27 08:06:05,156 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:05] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.036 0.022
2025-07-27 08:06:07,022 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.009
2025-07-27 08:06:07,036 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.014 0.022
2025-07-27 08:06:07,060 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.021 0.022
2025-07-27 08:06:07,078 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.041 0.026
2025-07-27 08:06:07,147 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 33 0.024 0.029
2025-07-27 08:06:07,176 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.001 0.007
2025-07-27 08:06:07,208 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.005 0.008
2025-07-27 08:06:07,279 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.022
2025-07-27 08:06:07,279 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.020
2025-07-27 08:06:07,296 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.043 0.023
2025-07-27 08:06:07,720 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.010 0.012
2025-07-27 08:06:07,756 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.022 0.023
2025-07-27 08:06:07,767 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.024
2025-07-27 08:06:07,819 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.067 0.037
2025-07-27 08:06:07,931 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 33 0.037 0.044
2025-07-27 08:06:07,958 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.001 0.009
2025-07-27 08:06:07,989 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:07] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.003 0.010
2025-07-27 08:06:08,033 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.016 0.012
2025-07-27 08:06:08,056 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.035 0.021
2025-07-27 08:06:08,063 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.022 0.021
2025-07-27 08:06:08,693 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.020 0.014
2025-07-27 08:06:08,699 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.023 0.016
2025-07-27 08:06:08,709 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.019
2025-07-27 08:06:08,722 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.029 0.030
2025-07-27 08:06:08,801 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 33 0.029 0.030
2025-07-27 08:06:08,831 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.003 0.006
2025-07-27 08:06:08,863 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.002 0.011
2025-07-27 08:06:08,918 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.020 0.016
2025-07-27 08:06:08,924 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.023 0.017
2025-07-27 08:06:08,929 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:08] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.033 0.017
2025-07-27 08:06:10,206 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:10] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.020
2025-07-27 08:06:10,206 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:10] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.006 0.018
2025-07-27 08:06:10,220 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:10] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.019
2025-07-27 08:06:10,237 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:10] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.019 0.025
2025-07-27 08:06:10,382 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:10] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 26 0.066 0.045
2025-07-27 08:06:10,475 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:10] "POST /web/dataset/call_kw/hr.jva.form/onchange HTTP/1.1" 200 - 22 0.040 0.025
2025-07-27 08:06:17,866 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:17] "POST /web/dataset/call_kw/hr.jva.form/onchange HTTP/1.1" 200 - 3 0.001 0.012
2025-07-27 08:06:33,060 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:33] "POST /web/dataset/call_kw/hr.jva.form/create HTTP/1.1" 200 - 27 0.068 0.040
2025-07-27 08:06:33,097 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:33] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 14 0.009 0.016
2025-07-27 08:06:33,209 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:33] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.047 0.036
2025-07-27 08:06:33,215 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:33] "POST /mail/thread/data HTTP/1.1" 200 - 19 0.061 0.025
2025-07-27 08:06:33,217 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:33] "POST /mail/thread/data HTTP/1.1" 200 - 14 0.046 0.027
2025-07-27 08:06:34,179 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:34] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.015 0.022
2025-07-27 08:06:34,224 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:34] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.044 0.035
2025-07-27 08:06:34,243 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:34] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.043 0.040
2025-07-27 08:06:34,331 115672 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.requested_by.work_email or user.email)|safe} as fallback 
2025-07-27 08:06:34,333 115672 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.requested_by.work_email or user.email)|safe} as fallback 
2025-07-27 08:06:38,483 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:38] "POST /web/dataset/call_button HTTP/1.1" 200 - 35 0.124 4.206
2025-07-27 08:06:38,592 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:38] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.013 0.016
2025-07-27 08:06:38,602 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:38] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.025 0.020
2025-07-27 08:06:45,648 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:45] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.005 0.014
2025-07-27 08:06:45,750 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:45] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 14 0.031 0.020
2025-07-27 08:06:45,830 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:45] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.009
2025-07-27 08:06:45,842 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:45] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.012 0.015
2025-07-27 08:06:50,904 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:50] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.015
2025-07-27 08:06:50,935 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:50] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.033 0.023
2025-07-27 08:06:50,974 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:50] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.038 0.031
2025-07-27 08:06:51,092 115672 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.approved_by.work_email or user.email)|safe} as fallback 
2025-07-27 08:06:51,095 115672 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.approved_by.work_email or user.email)|safe} as fallback 
2025-07-27 08:06:55,258 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:55] "POST /web/dataset/call_button HTTP/1.1" 200 - 44 0.125 4.237
2025-07-27 08:06:55,358 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:55] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.023 0.018
2025-07-27 08:06:55,380 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:55] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.046 0.019
2025-07-27 08:06:59,571 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:59] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 14 0.018 0.035
2025-07-27 08:06:59,707 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:59] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.007 0.013
2025-07-27 08:06:59,720 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:06:59] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.014 0.015
2025-07-27 08:07:04,836 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:04] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.008 0.014
2025-07-27 08:07:04,837 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:04] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.017 0.013
2025-07-27 08:07:04,862 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:04] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.021 0.030
2025-07-27 08:07:04,868 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:04] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.025 0.022
2025-07-27 08:07:05,033 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:05] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 51 0.076 0.090
2025-07-27 08:07:05,083 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:05] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 08:07:05,122 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:05] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.008 0.008
2025-07-27 08:07:05,130 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:05] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 7 0.013 0.014
2025-07-27 08:07:05,162 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:05] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.004 0.008
2025-07-27 08:07:05,198 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:05] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.023 0.028
2025-07-27 08:07:07,522 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:07] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 14 0.013 0.023
2025-07-27 08:07:07,572 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:07] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.007 0.008
2025-07-27 08:07:07,585 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:07] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.014 0.013
2025-07-27 08:07:08,862 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:08] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.017 0.014
2025-07-27 08:07:08,891 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:08] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.031 0.027
2025-07-27 08:07:08,900 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:08] "POST /web/dataset/call_button HTTP/1.1" 200 - 7 0.024 0.030
2025-07-27 08:07:08,925 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:08] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.037 0.031
2025-07-27 08:07:09,101 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:09] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.015 0.015
2025-07-27 08:07:09,124 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:09] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.033 0.024
2025-07-27 08:07:09,125 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:09] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 14 0.033 0.024
2025-07-27 08:07:09,135 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:09] "GET /jobs/detail/1 HTTP/1.1" 301 - 18 0.050 0.022
2025-07-27 08:07:09,180 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:09] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.009 0.013
2025-07-27 08:07:09,202 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:09] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.020 0.019
2025-07-27 08:07:11,074 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:11] "GET /jobs/detail/chief-executive-officer-1 HTTP/1.1" 200 - 191 0.244 1.663
2025-07-27 08:07:11,673 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:11] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.005 0.007
2025-07-27 08:07:18,480 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.007 0.012
2025-07-27 08:07:18,498 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.008 0.012
2025-07-27 08:07:18,503 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.021 0.017
2025-07-27 08:07:18,517 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.019 0.014
2025-07-27 08:07:18,550 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 4 0.002 0.022
2025-07-27 08:07:18,586 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.004 0.006
2025-07-27 08:07:18,622 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.010 0.007
2025-07-27 08:07:18,630 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.015 0.012
2025-07-27 08:07:18,668 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.008 0.006
2025-07-27 08:07:18,703 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:18] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 17 0.029 0.024
2025-07-27 08:07:20,450 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:20] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 14 0.015 0.023
2025-07-27 08:07:20,513 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.005 0.010
2025-07-27 08:07:20,533 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:20] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.011 0.021
2025-07-27 08:07:22,541 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:22] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 14 0.011 0.021
2025-07-27 08:07:22,621 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:22] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.007 0.011
2025-07-27 08:07:22,631 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:22] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.015 0.012
2025-07-27 08:07:25,981 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:25] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.010 0.027
2025-07-27 08:07:26,044 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:26] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.014 0.013
2025-07-27 08:07:26,078 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:26] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.027 0.030
2025-07-27 08:07:35,091 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:35] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 18 0.011 0.022
2025-07-27 08:07:35,137 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:35] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.002 0.011
2025-07-27 08:07:35,166 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:35] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.025 0.017
2025-07-27 08:07:36,938 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:36] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.009 0.011
2025-07-27 08:07:36,940 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:36] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.007 0.011
2025-07-27 08:07:36,988 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:36] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.007 0.009
2025-07-27 08:07:37,048 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /web/dataset/call_kw/hr.job/write HTTP/1.1" 200 - 8 0.006 0.030
2025-07-27 08:07:37,100 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 18 0.014 0.021
2025-07-27 08:07:37,147 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.014 0.008
2025-07-27 08:07:37,167 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.012 0.027
2025-07-27 08:07:37,188 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.026 0.039
2025-07-27 08:07:37,243 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.015 0.023
2025-07-27 08:07:37,292 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.010 0.011
2025-07-27 08:07:37,313 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.009 0.019
2025-07-27 08:07:37,326 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.035 0.022
2025-07-27 08:07:37,921 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.006 0.007
2025-07-27 08:07:37,932 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.012 0.010
2025-07-27 08:07:37,941 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.009 0.014
2025-07-27 08:07:37,963 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:37] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.028 0.021
2025-07-27 08:07:38,016 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.017 0.021
2025-07-27 08:07:38,066 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.011 0.015
2025-07-27 08:07:38,075 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.014 0.014
2025-07-27 08:07:38,096 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.034 0.022
2025-07-27 08:07:38,640 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.009 0.015
2025-07-27 08:07:38,646 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.011 0.015
2025-07-27 08:07:38,672 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.012 0.011
2025-07-27 08:07:38,702 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.031 0.027
2025-07-27 08:07:38,755 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.016 0.019
2025-07-27 08:07:38,812 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.012 0.015
2025-07-27 08:07:38,818 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.023 0.013
2025-07-27 08:07:38,832 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:38] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.033 0.020
2025-07-27 08:07:41,225 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:41] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.007 0.014
2025-07-27 08:07:41,225 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:41] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.008 0.012
2025-07-27 08:07:43,479 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /web/dataset/call_kw/hr.job/onchange HTTP/1.1" 200 - 4 0.007 0.017
2025-07-27 08:07:43,493 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.020
2025-07-27 08:07:43,502 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.012 0.025
2025-07-27 08:07:43,549 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.009 0.013
2025-07-27 08:07:43,567 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.012 0.017
2025-07-27 08:07:43,581 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /web/dataset/call_kw/hr.job/write HTTP/1.1" 200 - 8 0.011 0.024
2025-07-27 08:07:43,639 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.017 0.020
2025-07-27 08:07:43,669 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.005 0.011
2025-07-27 08:07:43,673 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.008 0.011
2025-07-27 08:07:43,674 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:43] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.011 0.006
2025-07-27 08:07:47,360 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:47] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.006 0.015
2025-07-27 08:07:47,364 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:47] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.016 0.012
2025-07-27 08:07:47,366 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:47] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.008 0.015
2025-07-27 08:07:47,372 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:47] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.010 0.017
2025-07-27 08:07:47,409 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:47] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.004 0.011
2025-07-27 08:07:47,466 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:47] "POST /web/dataset/call_kw/hr.jva.form/onchange HTTP/1.1" 200 - 14 0.015 0.018
2025-07-27 08:07:51,774 115672 WARNING ardano_hr2 odoo.http: Number of requested positions must be at least 1. 
2025-07-27 08:07:51,775 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:51] "POST /web/dataset/call_kw/hr.jva.form/create HTTP/1.1" 200 - 10 0.013 0.084
2025-07-27 08:07:55,543 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:55] "POST /web/dataset/call_kw/hr.jva.form/onchange HTTP/1.1" 200 - 3 0.001 0.011
2025-07-27 08:07:57,297 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:57] "POST /web/dataset/call_kw/hr.jva.form/create HTTP/1.1" 200 - 16 0.033 0.031
2025-07-27 08:07:57,338 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:57] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 9 0.007 0.015
2025-07-27 08:07:57,********** WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.requested_by.work_email or user.email)|safe} as fallback 
2025-07-27 08:07:57,476 115672 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.requested_by.work_email or user.email)|safe} as fallback 
2025-07-27 08:07:57,493 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:57] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.053 0.036
2025-07-27 08:07:57,507 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:07:57] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.075 0.036
2025-07-27 08:08:01,587 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:01] "POST /web/dataset/call_button HTTP/1.1" 200 - 21 0.047 4.180
2025-07-27 08:08:01,673 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:01] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.016 0.018
2025-07-27 08:08:01,689 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:01] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.029 0.024
2025-07-27 08:08:06,797 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:06] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 9 0.008 0.017
2025-07-27 08:08:06,915 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:06] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.009 0.015
2025-07-27 08:08:06,943 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:06] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.023 0.026
2025-07-27 08:08:09,945 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:09] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.009 0.012
2025-07-27 08:08:09,972 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:09] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.024 0.021
2025-07-27 08:08:09,989 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:09] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.019 0.030
2025-07-27 08:08:10,065 115672 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.approved_by.work_email or user.email)|safe} as fallback 
2025-07-27 08:08:10,066 115672 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.approved_by.work_email or user.email)|safe} as fallback 
2025-07-27 08:08:14,175 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:14] "POST /web/dataset/call_button HTTP/1.1" 200 - 32 0.074 4.173
2025-07-27 08:08:14,250 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:14] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.015 0.013
2025-07-27 08:08:14,264 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:14] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.027 0.018
2025-07-27 08:08:15,619 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:15] "GET /web HTTP/1.1" 200 - 17 0.023 0.037
2025-07-27 08:08:16,444 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.024 0.022
2025-07-27 08:08:16,480 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.014 0.022
2025-07-27 08:08:16,507 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.007 0.018
2025-07-27 08:08:16,511 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.011 0.018
2025-07-27 08:08:16,533 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.013 0.018
2025-07-27 08:08:16,579 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 11 0.007 0.017
2025-07-27 08:08:16,657 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.010 0.014
2025-07-27 08:08:16,669 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.021 0.017
2025-07-27 08:08:16,788 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:16] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.006 0.011
2025-07-27 08:08:18,409 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.012 0.017
2025-07-27 08:08:18,422 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "POST /web/dataset/call_button HTTP/1.1" 200 - 7 0.016 0.020
2025-07-27 08:08:18,429 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.023 0.023
2025-07-27 08:08:18,447 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.024 0.035
2025-07-27 08:08:18,561 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "GET /jobs/detail/5 HTTP/1.1" 301 - 7 0.018 0.015
2025-07-27 08:08:18,564 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 11 0.020 0.018
2025-07-27 08:08:18,566 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.021
2025-07-27 08:08:18,580 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.016 0.031
2025-07-27 08:08:18,639 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.020 0.034
2025-07-27 08:08:18,669 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:18] "GET /jobs/detail/human-resources-manager-5 HTTP/1.1" 200 - 25 0.030 0.053
2025-07-27 08:08:19,056 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:19] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 08:08:25,899 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:25] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.009
2025-07-27 08:08:25,909 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:25] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.008 0.015
2025-07-27 08:08:25,912 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:25] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.012 0.018
2025-07-27 08:08:25,925 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:25] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.010 0.025
2025-07-27 08:08:25,952 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:25] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 4 0.004 0.020
2025-07-27 08:08:25,996 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:25] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.003 0.009
2025-07-27 08:08:26,034 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:26] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.007 0.007
2025-07-27 08:08:26,041 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:26] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.009 0.014
2025-07-27 08:08:26,075 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:26] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.006 0.009
2025-07-27 08:08:26,116 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:26] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 15 0.035 0.023
2025-07-27 08:08:26,143 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:26] "POST /web/dataset/call_kw/hr.applicant.category/read HTTP/1.1" 200 - 4 0.004 0.007
2025-07-27 08:08:26,225 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:26] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.006 0.009
2025-07-27 08:08:30,758 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:30] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 11 0.010 0.017
2025-07-27 08:08:30,826 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:30] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.010 0.008
2025-07-27 08:08:30,835 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:30] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.012 0.014
2025-07-27 08:08:37,227 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:37] "POST /web/action/load HTTP/1.1" 200 - 13 0.028 0.017
2025-07-27 08:08:37,285 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:37] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.008 0.026
2025-07-27 08:08:37,355 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:37] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.008 0.014
2025-07-27 08:08:37,394 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:37] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.026 0.030
2025-07-27 08:08:42,665 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:42] "POST /web/dataset/call_button HTTP/1.1" 200 - 12 0.013 0.008
2025-07-27 08:08:42,698 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:42] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.009 0.010
2025-07-27 08:08:42,702 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:42] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 5 0.010 0.011
2025-07-27 08:08:42,703 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:42] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.005 0.015
2025-07-27 08:08:42,706 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:42] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.019
2025-07-27 08:08:42,707 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:42] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.004 0.013
2025-07-27 08:08:42,738 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:42] "GET /website/force/1?path=%2Fjobs%2Fdetail%2Fhuman-resources-manager-5 HTTP/1.1" 303 - 6 0.002 0.008
2025-07-27 08:08:42,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:42] "GET /jobs/detail/human-resources-manager-5 HTTP/1.1" 200 - 25 0.044 0.093
2025-07-27 08:08:43,232 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:43] "POST /web/dataset/call_kw/ir.model/get_available_models HTTP/1.1" 200 - 6 0.012 0.031
2025-07-27 08:08:43,321 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:43] "GET /website/iframefallback HTTP/1.1" 200 - 42 0.035 0.557
2025-07-27 08:08:43,386 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:43] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.004 0.008
2025-07-27 08:08:43,392 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:43] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.004 0.009
2025-07-27 08:08:43,985 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:43] "GET /web/bundle/website.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 31 0.021 0.360
2025-07-27 08:08:44,940 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:44] "GET /jobs/apply/human-resources-manager-5 HTTP/1.1" 200 - 42 0.064 0.179
2025-07-27 08:08:45,352 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:45] "POST /web/dataset/call_kw/res.users/read HTTP/1.1" 200 - 13 0.012 0.012
2025-07-27 08:08:45,398 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:45] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.009
2025-07-27 08:08:45,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:45] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.004 0.009
2025-07-27 08:08:45,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:45] "GET /web/bundle/website.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.003 0.005
2025-07-27 08:08:50,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:08:50] "GET /web/image/res.company/1/favicon HTTP/1.1" 200 - 11 0.018 0.024
2025-07-27 08:11:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:14] "POST /web/action/load HTTP/1.1" 200 - 14 0.022 0.034
2025-07-27 08:11:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:14] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 52 0.051 0.093
2025-07-27 08:11:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:14] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.006 0.013
2025-07-27 08:11:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:14] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 32 0.052 0.050
2025-07-27 08:11:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:14] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.003 0.006
2025-07-27 08:11:15,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753603874994 HTTP/1.1" 200 - 11 0.040 0.206
2025-07-27 08:11:15,441 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753603874995 HTTP/1.1" 200 - 11 0.037 0.203
2025-07-27 08:11:15,441 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753603874992 HTTP/1.1" 200 - 11 0.041 0.209
2025-07-27 08:11:15,468 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753603874996 HTTP/1.1" 200 - 6 0.019 0.236
2025-07-27 08:11:15,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753603874995 HTTP/1.1" 200 - 6 0.016 0.234
2025-07-27 08:11:15,480 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753603874996 HTTP/1.1" 200 - 6 0.027 0.246
2025-07-27 08:11:15,500 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753603874997 HTTP/1.1" 200 - 6 0.016 0.028
2025-07-27 08:11:15,508 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753603874996 HTTP/1.1" 200 - 6 0.027 0.031
2025-07-27 08:11:15,510 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753603874997 HTTP/1.1" 200 - 6 0.019 0.030
2025-07-27 08:11:15,519 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753603874997 HTTP/1.1" 200 - 6 0.014 0.031
2025-07-27 08:11:15,520 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753603874998 HTTP/1.1" 200 - 6 0.014 0.025
2025-07-27 08:11:15,548 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753603874999 HTTP/1.1" 200 - 6 0.016 0.042
2025-07-27 08:11:15,549 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753603874999 HTTP/1.1" 200 - 6 0.021 0.019
2025-07-27 08:11:15,566 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753603874999 HTTP/1.1" 200 - 6 0.013 0.037
2025-07-27 08:11:15,579 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753603875000 HTTP/1.1" 200 - 6 0.016 0.032
2025-07-27 08:11:15,583 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753603875000 HTTP/1.1" 200 - 6 0.012 0.044
2025-07-27 08:11:15,591 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753603875000 HTTP/1.1" 200 - 6 0.016 0.040
2025-07-27 08:11:15,604 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753603875001 HTTP/1.1" 200 - 6 0.013 0.027
2025-07-27 08:11:15,609 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753603875001 HTTP/1.1" 200 - 6 0.013 0.022
2025-07-27 08:11:15,609 115672 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:11:15] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753603875001 HTTP/1.1" 200 - 6 0.008 0.023
2025-07-27 08:12:46,559 140048 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 08:12:46,559 140048 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 08:12:46,559 140048 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 08:12:46,559 140048 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 08:12:46,924 140048 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 08:12:47,274 140048 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 08:12:47,395 140048 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 08:12:47,395 140048 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 08:12:47,442 140048 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 08:12:47,442 140048 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 08:12:51,045 140048 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 08:12:51,045 140048 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 08:12:53,539 140048 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 08:12:53,631 140048 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 08:12:53,681 140048 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 08:12:56,222 140048 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 08:12:56,552 140048 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 08:12:56,802 140048 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 08:12:57,412 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 08:12:57,537 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 08:12:57,563 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 08:12:57,588 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 08:12:57,609 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 08:12:57,627 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 08:12:57,639 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 08:12:57,646 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 08:12:57,655 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 08:12:57,680 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 08:12:57,695 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 08:12:57,706 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 08:12:57,737 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 08:12:57,746 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 08:12:57,754 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 08:12:57,916 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 08:12:58,005 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 08:12:58,049 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 08:12:58,087 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 08:12:58,154 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 08:12:58,188 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 08:12:58,227 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 08:12:58,330 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 08:12:58,516 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 08:12:58,573 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 08:12:58,631 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 08:12:58,657 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 08:12:58,747 140048 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 08:12:59,026 140048 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.80s, 908 queries (+908 other) 
2025-07-27 08:12:59,027 140048 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 5.40s, 908 queries (+908 extra) 
2025-07-27 08:13:00,964 140048 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 08:13:00,989 140048 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 13.736s 
2025-07-27 08:13:01,150 140048 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 08:13:01,151 140048 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 08:13:03,316 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:03] "GET /websocket HTTP/1.1" 101 - 8 0.018 15.792
2025-07-27 08:13:03,********** INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 08:13:06,411 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:06] "GET /web HTTP/1.1" 200 - 177 0.288 18.618
2025-07-27 08:13:07,124 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "GET /web/webclient/translations/d79ae96871618198c190218bdf3011f4ff6f8d28?lang=en_US HTTP/1.1" 200 - 3 0.003 0.471
2025-07-27 08:13:07,134 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "GET /web/webclient/load_menus/f855969452d5e4918fa4816d22948eaf11cb3041ba0dba1b5590331945ce61cd HTTP/1.1" 200 - 6 0.010 0.475
2025-07-27 08:13:07,207 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.006
2025-07-27 08:13:07,305 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "POST /web/action/load HTTP/1.1" 200 - 16 0.024 0.028
2025-07-27 08:13:07,532 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.130 0.096
2025-07-27 08:13:07,624 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 8 0.042 0.152
2025-07-27 08:13:07,624 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 39 0.138 0.087
2025-07-27 08:13:07,651 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "POST /mail/load_message_failures HTTP/1.1" 200 - 12 0.016 0.062
2025-07-27 08:13:07,731 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 58 0.054 0.163
2025-07-27 08:13:07,882 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 14 0.068 0.019
2025-07-27 08:13:07,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:07] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 35 0.086 0.112
2025-07-27 08:13:08,060 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.005 0.012
2025-07-27 08:13:08,445 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753603988135 HTTP/1.1" 200 - 11 0.011 0.086
2025-07-27 08:13:08,445 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753603988129 HTTP/1.1" 200 - 11 0.028 0.071
2025-07-27 08:13:08,446 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753603988135 HTTP/1.1" 200 - 6 0.019 0.077
2025-07-27 08:13:08,495 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753603988134 HTTP/1.1" 200 - 6 0.028 0.111
2025-07-27 08:13:08,496 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753603988135 HTTP/1.1" 200 - 6 0.023 0.116
2025-07-27 08:13:08,512 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753603988134 HTTP/1.1" 200 - 6 0.034 0.124
2025-07-27 08:13:08,541 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753603988136 HTTP/1.1" 200 - 6 0.027 0.047
2025-07-27 08:13:08,542 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753603988137 HTTP/1.1" 200 - 6 0.025 0.036
2025-07-27 08:13:08,542 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753603988136 HTTP/1.1" 200 - 6 0.046 0.040
2025-07-27 08:13:08,565 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753603988138 HTTP/1.1" 200 - 6 0.015 0.041
2025-07-27 08:13:08,570 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753603988139 HTTP/1.1" 200 - 6 0.016 0.025
2025-07-27 08:13:08,576 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753603988138 HTTP/1.1" 200 - 6 0.011 0.041
2025-07-27 08:13:08,608 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753603988139 HTTP/1.1" 200 - 6 0.015 0.032
2025-07-27 08:13:08,616 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753603988139 HTTP/1.1" 200 - 6 0.022 0.042
2025-07-27 08:13:08,618 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753603988140 HTTP/1.1" 200 - 6 0.022 0.023
2025-07-27 08:13:08,620 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753603988139 HTTP/1.1" 200 - 6 0.020 0.035
2025-07-27 08:13:08,625 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753603988140 HTTP/1.1" 200 - 6 0.020 0.026
2025-07-27 08:13:08,639 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753603988141 HTTP/1.1" 200 - 6 0.014 0.029
2025-07-27 08:13:08,651 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753603988141 HTTP/1.1" 200 - 6 0.010 0.022
2025-07-27 08:13:08,656 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:08] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753603988141 HTTP/1.1" 200 - 6 0.009 0.016
2025-07-27 08:13:16,061 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:16] "POST /web/action/load HTTP/1.1" 200 - 12 0.018 0.017
2025-07-27 08:13:16,137 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:16] "POST /web/dataset/call_kw/hr.division/get_views HTTP/1.1" 200 - 17 0.019 0.035
2025-07-27 08:13:16,187 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:16] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 4 0.004 0.008
2025-07-27 08:13:16,226 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:16] "POST /web/dataset/call_kw/hr.division/web_search_read HTTP/1.1" 200 - 20 0.035 0.017
2025-07-27 08:13:19,411 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:19] "POST /web/dataset/call_kw/hr.division/onchange HTTP/1.1" 200 - 6 0.007 0.018
2025-07-27 08:13:20,808 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:20] "POST /web/dataset/call_kw/hr.division/web_search_read HTTP/1.1" 200 - 15 0.040 0.013
2025-07-27 08:13:22,366 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:22] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 12 0.014 0.022
2025-07-27 08:13:22,407 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:22] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 7 0.011 0.011
2025-07-27 08:13:22,442 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:22] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 4 0.002 0.008
2025-07-27 08:13:24,382 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:24] "POST /web/dataset/call_button HTTP/1.1" 200 - 6 0.014 0.011
2025-07-27 08:13:24,451 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:24] "POST /web/dataset/call_kw/hr.department/get_views HTTP/1.1" 200 - 14 0.021 0.026
2025-07-27 08:13:24,491 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:24] "POST /web/dataset/call_kw/hr.department/web_search_read HTTP/1.1" 200 - 7 0.015 0.011
2025-07-27 08:13:25,358 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:25] "POST /web/dataset/call_kw/hr.division/get_views HTTP/1.1" 200 - 4 0.009 0.010
2025-07-27 08:13:25,429 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:25] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 12 0.024 0.014
2025-07-27 08:13:25,473 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:25] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 7 0.009 0.008
2025-07-27 08:13:25,504 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:25] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 4 0.002 0.008
2025-07-27 08:13:26,412 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:26] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.004 0.007
2025-07-27 08:13:26,643 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:26] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 53 0.096 0.082
2025-07-27 08:13:26,682 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:26] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.007 0.011
2025-07-27 08:13:26,724 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:26] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.039 0.018
2025-07-27 08:13:27,611 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:27] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 12 0.027 0.015
2025-07-27 08:13:27,644 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:27] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 7 0.006 0.011
2025-07-27 08:13:27,672 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:27] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 08:13:28,290 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:28] "POST /web/dataset/call_button HTTP/1.1" 200 - 9 0.007 0.014
2025-07-27 08:13:28,380 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:28] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 8 0.015 0.029
2025-07-27 08:13:28,443 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:28] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.007 0.012
2025-07-27 08:13:28,512 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:28] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 19 0.051 0.033
2025-07-27 08:13:28,553 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:28] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.003 0.011
2025-07-27 08:13:28,561 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:28] "POST /web/dataset/call_kw/hr.unit/read HTTP/1.1" 200 - 4 0.007 0.012
2025-07-27 08:13:28,563 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:28] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.010 0.009
2025-07-27 08:13:29,284 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:29] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 12 0.015 0.022
2025-07-27 08:13:29,318 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:29] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 7 0.006 0.010
2025-07-27 08:13:29,352 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:29] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 4 0.004 0.007
2025-07-27 08:13:33,234 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:33] "POST /web/action/load HTTP/1.1" 200 - 14 0.061 0.022
2025-07-27 08:13:33,334 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:33] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 26 0.040 0.042
2025-07-27 08:13:33,394 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:33] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 13 0.013 0.018
2025-07-27 08:13:35,761 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:35] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 12 0.034 0.020
2025-07-27 08:13:35,891 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:35] "POST /mail/thread/data HTTP/1.1" 200 - 24 0.044 0.025
2025-07-27 08:13:35,954 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:35] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.107 0.030
2025-07-27 08:13:36,042 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:36] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.007 0.009
2025-07-27 08:13:39,535 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:39] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 9 0.018 0.014
2025-07-27 08:13:41,216 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:41] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 14 0.021 0.021
2025-07-27 08:13:41,287 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:41] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.024 0.017
2025-07-27 08:13:41,348 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:41] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.072 0.033
2025-07-27 08:13:42,490 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:42] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 9 0.010 0.014
2025-07-27 08:13:46,040 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:46] "POST /web/action/load HTTP/1.1" 200 - 11 0.009 0.016
2025-07-27 08:13:46,081 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:46] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.008 0.014
2025-07-27 08:13:46,118 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:46] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 4 0.004 0.007
2025-07-27 08:13:49,370 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:49] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 9 0.009 0.013
2025-07-27 08:13:54,688 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:54] "POST /web/action/load HTTP/1.1" 200 - 13 0.042 0.012
2025-07-27 08:13:54,735 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:54] "POST /web/dataset/call_kw/hr.job.category/get_views HTTP/1.1" 200 - 16 0.016 0.016
2025-07-27 08:13:54,783 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:54] "POST /web/dataset/call_kw/hr.job.category/web_search_read HTTP/1.1" 200 - 6 0.012 0.005
2025-07-27 08:13:55,764 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:13:55] "POST /web/dataset/call_kw/hr.job.category/read HTTP/1.1" 200 - 4 0.011 0.000
2025-07-27 08:14:07,474 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:07] "POST /web/dataset/call_kw/hr.job.category/web_search_read HTTP/1.1" 200 - 5 0.005 0.011
2025-07-27 08:14:09,985 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:09] "POST /web/dataset/call_kw/hr.job.category/read HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 08:14:13,684 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:13] "POST /web/action/load HTTP/1.1" 200 - 12 0.018 0.020
2025-07-27 08:14:13,754 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:13] "POST /web/dataset/call_kw/hr.job.grade/get_views HTTP/1.1" 200 - 14 0.027 0.025
2025-07-27 08:14:13,815 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:13] "POST /web/dataset/call_kw/hr.job.grade/web_search_read HTTP/1.1" 200 - 21 0.024 0.015
2025-07-27 08:14:21,138 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:21] "POST /web/action/load HTTP/1.1" 200 - 13 0.008 0.017
2025-07-27 08:14:21,243 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:21] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 17 0.019 0.061
2025-07-27 08:14:21,302 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:21] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.010 0.009
2025-07-27 08:14:21,347 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:21] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.035 0.024
2025-07-27 08:14:23,203 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:23] "POST /web/action/load HTTP/1.1" 200 - 15 0.013 0.018
2025-07-27 08:14:23,356 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:23] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 59 0.042 0.093
2025-07-27 08:14:23,390 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:23] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.003 0.008
2025-07-27 08:14:23,428 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:23] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.007 0.008
2025-07-27 08:14:23,439 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:23] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 7 0.018 0.010
2025-07-27 08:14:23,470 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:23] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.007 0.005
2025-07-27 08:14:23,518 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:23] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 19 0.037 0.025
2025-07-27 08:14:23,597 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:23] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.006 0.015
2025-07-27 08:14:24,591 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:24] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.005 0.025
2025-07-27 08:14:24,641 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:24] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.007 0.012
2025-07-27 08:14:24,670 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:24] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.026 0.021
2025-07-27 08:14:29,639 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:29] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 36 0.046 0.029
2025-07-27 08:14:29,674 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:29] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.007 0.006
2025-07-27 08:14:29,719 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:29] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 8 0.016 0.010
2025-07-27 08:14:29,829 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:29] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.035 0.017
2025-07-27 08:14:29,867 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:29] "POST /mail/thread/messages HTTP/1.1" 200 - 31 0.059 0.033
2025-07-27 08:14:30,259 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:30] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.051 0.424
2025-07-27 08:14:30,358 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:30] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.008 0.012
2025-07-27 08:14:33,197 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:33] "POST /web/action/load HTTP/1.1" 200 - 12 0.015 0.015
2025-07-27 08:14:33,239 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:33] "POST /report/check_wkhtmltopdf HTTP/1.1" 200 - 3 0.003 0.006
2025-07-27 08:14:34,473 140048 INFO ardano_hr2 odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-ebdf73e/1/web.report_assets_common.min.css 
2025-07-27 08:14:53,996 140048 INFO ardano_hr2 odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-ebdf73e/1/web.report_assets_common.min.js 
2025-07-27 08:14:54,552 140048 INFO ardano_hr2 odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-5307707/1/web.report_assets_pdf.min.css 
2025-07-27 08:14:55,404 140048 INFO ardano_hr2 odoo.addons.base.models.ir_actions_report: Generating PDF on Windows platform require DPI >= 96. Using 96 instead. 
2025-07-27 08:14:58,095 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:58] "GET /web/assets/763-5307707/1/web.report_assets_pdf.min.css HTTP/1.1" 200 - 5 0.010 0.014
2025-07-27 08:14:58,157 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:14:58] "GET /web/assets/761-ebdf73e/1/web.report_assets_common.min.css HTTP/1.1" 200 - 5 0.009 0.075
2025-07-27 08:15:01,032 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:01] "GET / HTTP/1.1" 200 - 188 0.410 2.376
2025-07-27 08:15:04,953 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:04] "GET /web/static/fonts/lato/Lato-Hai-webfont.woff HTTP/1.1" 200 - 0 0.000 0.026
2025-07-27 08:15:04,954 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:04] "GET /web/static/fonts/lato/Lato-Lig-webfont.woff HTTP/1.1" 200 - 0 0.000 0.028
2025-07-27 08:15:04,955 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:04] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.031
2025-07-27 08:15:04,955 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:04] "GET /web/static/fonts/lato/Lato-Bla-webfont.woff HTTP/1.1" 200 - 0 0.000 0.026
2025-07-27 08:15:04,956 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:04] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.028
2025-07-27 08:15:06,579 140048 INFO ardano_hr2 odoo.addons.base.models.ir_actions_report: The PDF report has been generated for model: hr.job, records [1]. 
2025-07-27 08:15:06,586 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:06] "POST /report/download HTTP/1.1" 200 - 219 0.566 32.755
2025-07-27 08:15:06,719 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:06] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 34 0.054 0.033
2025-07-27 08:15:06,774 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:06] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.004 0.010
2025-07-27 08:15:06,812 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:06] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.005 0.011
2025-07-27 08:15:06,845 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:06] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.008
2025-07-27 08:15:06,861 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:06] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.015 0.013
2025-07-27 08:15:23,244 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:23] "GET / HTTP/1.1" 200 - 23 0.035 0.049
2025-07-27 08:15:23,778 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:23] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.004 0.008
2025-07-27 08:15:25,979 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:25] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.006 0.009
2025-07-27 08:15:27,024 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:27] "GET /jobs HTTP/1.1" 200 - 44 0.046 0.184
2025-07-27 08:15:27,243 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:27] "GET /website_hr_recruitment/static/src/img/job_image_1.jpg HTTP/1.1" 200 - 0 0.000 0.005
2025-07-27 08:15:27,497 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:27] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 08:15:46,056 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:46] "GET /jobs HTTP/1.1" 200 - 142 0.201 1.555
2025-07-27 08:15:46,147 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:46] "GET /website_hr_recruitment/static/src/img/job_image_1.jpg HTTP/1.1" 200 - 0 0.000 0.002
2025-07-27 08:15:50,343 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:50] "GET /jobs/detail/consultant-3 HTTP/1.1" 200 - 18 0.036 0.117
2025-07-27 08:15:50,510 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:50] "GET /website_hr_recruitment/static/src/img/job_image_3.jpg HTTP/1.1" 200 - 0 0.000 0.004
2025-07-27 08:15:50,736 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:50] "GET /website_hr_recruitment/static/src/img/job_image_4.jpg HTTP/1.1" 200 - 0 0.000 0.011
2025-07-27 08:15:50,737 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:50] "GET /website_hr_recruitment/static/src/img/job_image_6.jpg HTTP/1.1" 200 - 0 0.000 0.008
2025-07-27 08:15:50,738 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:50] "GET /website_hr_recruitment/static/src/img/job_image_2.jpg HTTP/1.1" 200 - 0 0.000 0.013
2025-07-27 08:15:50,739 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 08:15:50] "GET /website_hr_recruitment/static/src/img/job_image_5.jpg HTTP/1.1" 200 - 0 0.000 0.009
2025-07-27 08:16:02,795 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:02] "GET /jobs/detail/experienced-developer-4 HTTP/1.1" 200 - 12 0.020 0.040
2025-07-27 08:16:06,570 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:06] "GET /jobs/detail/chief-executive-officer-1 HTTP/1.1" 200 - 15 0.024 0.035
2025-07-27 08:16:08,547 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:08] "GET /jobs/apply/chief-executive-officer-1 HTTP/1.1" 200 - 21 0.040 0.131
2025-07-27 08:16:23,533 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:23] "GET /web HTTP/1.1" 200 - 17 0.023 0.023
2025-07-27 08:16:24,626 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:24] "POST /web/action/load HTTP/1.1" 200 - 19 0.029 0.038
2025-07-27 08:16:24,700 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:24] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.031 0.047
2025-07-27 08:16:24,779 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:24] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.025 0.010
2025-07-27 08:16:24,871 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:24] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.131 0.035
2025-07-27 08:16:24,892 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:24] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.033 0.059
2025-07-27 08:16:24,976 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:24] "POST /web/dataset/call_kw/mail.mail/get_views HTTP/1.1" 200 - 29 0.047 0.060
2025-07-27 08:16:25,012 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:25] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.000 0.000
2025-07-27 08:16:25,060 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:25] "POST /web/dataset/call_kw/mail.mail/web_search_read HTTP/1.1" 200 - 9 0.021 0.027
2025-07-27 08:16:27,331 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:27] "POST /web/dataset/call_kw/mail.mail/read HTTP/1.1" 200 - 8 0.021 0.038
2025-07-27 08:16:27,532 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:27] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 3 0.000 0.012
2025-07-27 08:16:35,542 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:35] "GET /web HTTP/1.1" 200 - 17 0.021 0.026
2025-07-27 08:16:36,452 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:36] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.018 0.048
2025-07-27 08:16:36,453 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:36] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.104 0.030
2025-07-27 08:16:36,478 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:36] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.016 0.067
2025-07-27 08:16:36,489 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:36] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.081 0.042
2025-07-27 08:16:36,562 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 08:16:36] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.017 0.018
2025-07-27 08:56:29,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 08:56:29,370 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 08:56:29,376 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 08:56:29,393 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 08:56:29,396 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 08:56:29,410 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 08:56:29,414 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 08:56:29,419 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 08:56:29,429 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 08:56:29,441 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 09:16:20,104 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `AlWasead: Check Staffing Levels`. 
2025-07-27 09:16:20,116 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `AlWasead: Check Staffing Levels` done. 
2025-07-27 09:16:31,237 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:16:31] "GET /websocket HTTP/1.1" 101 - 2 0.008 0.012
2025-07-27 09:47:59,483 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 09:47:59,506 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 09:47:59,521 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 09:47:59,533 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 09:47:59,545 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 09:47:59,547 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 09:47:59,561 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 09:47:59,561 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 09:47:59,572 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 09:47:59,579 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 09:58:43,323 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "POST /web/dataset/call_button HTTP/1.1" 200 - 12 0.016 0.027
2025-07-27 09:58:43,355 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.016 0.020
2025-07-27 09:58:43,372 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.022 0.026
2025-07-27 09:58:43,461 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.013 0.015
2025-07-27 09:58:43,476 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.006 0.024
2025-07-27 09:58:43,477 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 5 0.010 0.028
2025-07-27 09:58:43,483 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.011 0.028
2025-07-27 09:58:43,487 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.010 0.021
2025-07-27 09:58:43,541 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "GET /website/force/1?path=%2Fjobs%2Fdetail%2Fchief-executive-officer-1 HTTP/1.1" 303 - 6 0.006 0.017
2025-07-27 09:58:43,879 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "GET /jobs/detail/chief-executive-officer-1 HTTP/1.1" 200 - 39 0.126 0.194
2025-07-27 09:58:43,965 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:43] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.034
2025-07-27 09:58:44,229 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:44] "POST /web/dataset/call_kw/ir.model/get_available_models HTTP/1.1" 200 - 6 0.012 0.040
2025-07-27 09:58:44,322 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:44] "GET /website/iframefallback HTTP/1.1" 200 - 42 0.074 0.730
2025-07-27 09:58:44,562 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:44] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.004 0.006
2025-07-27 09:58:44,567 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:44] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.004 0.008
2025-07-27 09:58:45,197 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:45] "GET /web/bundle/website.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 31 0.034 0.428
2025-07-27 09:58:47,508 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:47] "GET /jobs/apply/chief-executive-officer-1 HTTP/1.1" 200 - 34 0.050 0.211
2025-07-27 09:58:47,554 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:47] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.004
2025-07-27 09:58:47,910 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:47] "POST /web/dataset/call_kw/res.users/read HTTP/1.1" 200 - 13 0.015 0.012
2025-07-27 09:58:47,978 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:47] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.006 0.010
2025-07-27 09:58:47,979 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:47] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.004 0.008
2025-07-27 09:58:48,088 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:58:48] "GET /web/bundle/website.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.003 0.008
2025-07-27 09:59:06,790 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:06] "GET /jobs/detail/chief-executive-officer-1 HTTP/1.1" 200 - 28 0.036 0.099
2025-07-27 09:59:07,092 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:07] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.000 0.000
2025-07-27 09:59:07,106 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:07] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.000 0.000
2025-07-27 09:59:07,234 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:07] "GET /web/bundle/website.assets_wysiwyg?lang=en_US&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.004
2025-07-27 09:59:10,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:10] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.015 0.018
2025-07-27 09:59:10,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:10] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.026 0.048
2025-07-27 09:59:11,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:11] "GET /web/image/res.company/1/favicon HTTP/1.1" 200 - 11 0.003 0.032
2025-07-27 09:59:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:14] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.012 0.000
2025-07-27 09:59:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:14] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.032 0.000
2025-07-27 09:59:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:14] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.000 0.032
2025-07-27 09:59:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:14] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.000 0.010
2025-07-27 09:59:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:14] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 17 0.057 0.000
2025-07-27 09:59:21,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:21] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.055 0.009
2025-07-27 09:59:21,871 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:21] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.013 0.001
2025-07-27 09:59:22,023 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:22] "POST /mail/thread/data HTTP/1.1" 200 - 25 0.039 0.031
2025-07-27 09:59:22,032 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:22] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.049 0.029
2025-07-27 09:59:22,189 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:22] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.016 0.002
2025-07-27 09:59:22,214 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:22] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.023 0.019
2025-07-27 09:59:22,277 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:22] "GET /web/image/760?unique=1 HTTP/1.1" 200 - 8 0.019 0.027
2025-07-27 09:59:22,335 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:22] "GET /web/image/759/1920x160 HTTP/1.1" 200 - 8 0.013 0.089
2025-07-27 09:59:22,335 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 09:59:22] "GET /web/image/760/1920x160 HTTP/1.1" 200 - 8 0.012 0.093
2025-07-27 11:16:38,803 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 11:16:38,944 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 11:16:38,958 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 11:16:38,968 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 11:16:38,982 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 11:16:38,990 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 11:16:39,001 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 11:16:39,011 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 11:16:39,021 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 11:16:39,029 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 11:16:53,793 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:16:53] "GET /websocket HTTP/1.1" 101 - 2 0.001 0.015
2025-07-27 11:29:15,439 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 11:29:15,451 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 11:29:15,468 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 11:29:15,477 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 11:29:39,158 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 11:29:39,176 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 11:29:39,203 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 11:29:39,214 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 11:29:39,237 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 11:29:39,249 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 11:36:46,385 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:46] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 4 0.011 0.197
2025-07-27 11:36:46,514 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:46] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.033 0.032
2025-07-27 11:36:46,706 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:46] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 22 0.088 0.163
2025-07-27 11:36:46,797 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:46] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.012 0.020
2025-07-27 11:36:47,156 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753616206851 HTTP/1.1" 200 - 6 0.023 0.039
2025-07-27 11:36:47,160 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753616206858 HTTP/1.1" 200 - 6 0.026 0.038
2025-07-27 11:36:47,170 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753616206855 HTTP/1.1" 200 - 6 0.029 0.042
2025-07-27 11:36:47,190 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753616206862 HTTP/1.1" 200 - 6 0.019 0.056
2025-07-27 11:36:47,195 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753616206856 HTTP/1.1" 200 - 6 0.037 0.053
2025-07-27 11:36:47,199 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753616206859 HTTP/1.1" 200 - 6 0.029 0.040
2025-07-27 11:36:47,246 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753616206864 HTTP/1.1" 200 - 6 0.022 0.045
2025-07-27 11:36:47,246 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753616206863 HTTP/1.1" 200 - 6 0.019 0.055
2025-07-27 11:36:47,258 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753616206865 HTTP/1.1" 200 - 6 0.030 0.037
2025-07-27 11:36:47,266 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753616206866 HTTP/1.1" 200 - 6 0.023 0.032
2025-07-27 11:36:47,274 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753616206866 HTTP/1.1" 200 - 6 0.023 0.050
2025-07-27 11:36:47,277 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753616206867 HTTP/1.1" 200 - 6 0.020 0.032
2025-07-27 11:36:47,308 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753616206869 HTTP/1.1" 200 - 6 0.008 0.030
2025-07-27 11:36:47,340 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753616206871 HTTP/1.1" 200 - 6 0.015 0.054
2025-07-27 11:36:47,348 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753616206871 HTTP/1.1" 200 - 6 0.022 0.045
2025-07-27 11:36:47,353 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753616206872 HTTP/1.1" 200 - 6 0.021 0.045
2025-07-27 11:36:47,353 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753616206872 HTTP/1.1" 200 - 6 0.026 0.044
2025-07-27 11:36:47,358 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753616206873 HTTP/1.1" 200 - 6 0.019 0.043
2025-07-27 11:36:47,368 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753616206874 HTTP/1.1" 200 - 6 0.018 0.035
2025-07-27 11:36:47,379 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:47] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753616206874 HTTP/1.1" 200 - 6 0.008 0.013
2025-07-27 11:36:53,674 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:36:53] "POST /web/dataset/call_kw/hr.job.category/web_search_read HTTP/1.1" 200 - 5 0.009 0.021
2025-07-27 11:37:02,866 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:37:02] "POST /web/dataset/call_kw/hr.job.category/read HTTP/1.1" 200 - 4 0.003 0.011
2025-07-27 11:37:49,586 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:37:49] "POST /web/dataset/call_kw/hr.job.category/write HTTP/1.1" 200 - 6 0.034 0.051
2025-07-27 11:37:49,618 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:37:49] "POST /web/dataset/call_kw/hr.job.category/web_search_read HTTP/1.1" 200 - 5 0.000 0.010
2025-07-27 11:38:35,061 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:38:35] "POST /web/dataset/call_kw/hr.job.category/read HTTP/1.1" 200 - 4 0.031 0.006
2025-07-27 11:44:01,176 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:44:01] "POST /web/dataset/call_kw/hr.job.grade/web_search_read HTTP/1.1" 200 - 20 0.053 0.028
2025-07-27 11:44:08,416 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:44:08] "POST /web/dataset/call_kw/hr.job.grade/read HTTP/1.1" 200 - 7 0.013 0.014
2025-07-27 11:44:14,199 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:44:14] "POST /web/dataset/call_kw/hr.job.grade/web_search_read HTTP/1.1" 200 - 20 0.032 0.027
2025-07-27 11:44:16,449 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:44:16] "POST /web/dataset/call_kw/hr.job.grade/read HTTP/1.1" 200 - 7 0.007 0.013
2025-07-27 11:45:17,755 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:45:17] "POST /web/dataset/call_kw/hr.job.grade/web_search_read HTTP/1.1" 200 - 20 0.013 0.032
2025-07-27 11:45:24,120 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:45:24] "POST /web/dataset/call_kw/hr.job.category/web_search_read HTTP/1.1" 200 - 5 0.004 0.010
2025-07-27 11:45:25,993 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:45:25] "POST /web/dataset/call_kw/hr.job.category/read HTTP/1.1" 200 - 4 0.003 0.009
2025-07-27 11:45:30,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:45:30] "POST /web/dataset/call_kw/hr.job.category/web_search_read HTTP/1.1" 200 - 5 0.007 0.010
2025-07-27 11:46:09,563 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:09] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.016 0.017
2025-07-27 11:46:09,678 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:09] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 22 0.064 0.076
2025-07-27 11:46:09,744 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:09] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.007 0.011
2025-07-27 11:46:10,003 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753616769780 HTTP/1.1" 200 - 6 0.015 0.026
2025-07-27 11:46:10,027 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753616769781 HTTP/1.1" 200 - 6 0.015 0.044
2025-07-27 11:46:10,038 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753616769781 HTTP/1.1" 200 - 6 0.026 0.042
2025-07-27 11:46:10,045 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753616769782 HTTP/1.1" 200 - 6 0.023 0.045
2025-07-27 11:46:10,050 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753616769781 HTTP/1.1" 200 - 6 0.029 0.037
2025-07-27 11:46:10,053 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753616769782 HTTP/1.1" 200 - 6 0.024 0.055
2025-07-27 11:46:10,076 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753616769782 HTTP/1.1" 200 - 6 0.015 0.037
2025-07-27 11:46:10,087 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753616769783 HTTP/1.1" 200 - 6 0.015 0.031
2025-07-27 11:46:10,116 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753616769783 HTTP/1.1" 200 - 6 0.019 0.048
2025-07-27 11:46:10,126 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753616769783 HTTP/1.1" 200 - 6 0.023 0.038
2025-07-27 11:46:10,127 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753616769783 HTTP/1.1" 200 - 6 0.019 0.033
2025-07-27 11:46:10,148 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753616769784 HTTP/1.1" 200 - 6 0.023 0.048
2025-07-27 11:46:10,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753616769784 HTTP/1.1" 200 - 6 0.023 0.044
2025-07-27 11:46:10,169 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753616769785 HTTP/1.1" 200 - 6 0.019 0.040
2025-07-27 11:46:10,189 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753616769786 HTTP/1.1" 200 - 6 0.019 0.043
2025-07-27 11:46:10,196 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753616769786 HTTP/1.1" 200 - 6 0.017 0.034
2025-07-27 11:46:10,217 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753616769787 HTTP/1.1" 200 - 6 0.019 0.045
2025-07-27 11:46:10,223 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753616769787 HTTP/1.1" 200 - 6 0.017 0.029
2025-07-27 11:46:10,226 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753616769788 HTTP/1.1" 200 - 6 0.011 0.031
2025-07-27 11:46:10,229 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:10] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753616769788 HTTP/1.1" 200 - 6 0.012 0.024
2025-07-27 11:46:11,868 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:11] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 36 0.099 0.070
2025-07-27 11:46:11,906 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:11] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.008 0.009
2025-07-27 11:46:11,910 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:11] "POST /web/dataset/call_kw/hr.unit/read HTTP/1.1" 200 - 4 0.009 0.008
2025-07-27 11:46:11,949 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:11] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 5 0.008 0.011
2025-07-27 11:46:11,952 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:11] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.011 0.013
2025-07-27 11:46:12,073 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:12] "POST /hr/get_org_chart HTTP/1.1" 200 - 14 0.029 0.032
2025-07-27 11:46:12,104 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:12] "POST /mail/thread/data HTTP/1.1" 200 - 17 0.047 0.033
2025-07-27 11:46:12,121 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:12] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.063 0.039
2025-07-27 11:46:12,178 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:12] "GET /web/image/hr.employee.public/20/avatar_1024/ HTTP/1.1" 304 - 11 0.020 0.017
2025-07-27 11:46:12,179 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:46:12] "GET /web/image/hr.employee.public/1/avatar_1024/ HTTP/1.1" 304 - 11 0.012 0.028
2025-07-27 11:47:00,459 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:00] "POST /web/dataset/call_kw/hr.division/web_search_read HTTP/1.1" 200 - 15 0.049 0.029
2025-07-27 11:47:02,080 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:02] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 12 0.013 0.018
2025-07-27 11:47:02,116 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:02] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 7 0.008 0.008
2025-07-27 11:47:02,149 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:02] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 4 0.004 0.010
2025-07-27 11:47:14,577 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:14] "POST /web/dataset/call_kw/hr.employee/name_search HTTP/1.1" 200 - 5 0.004 0.014
2025-07-27 11:47:20,947 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:20] "POST /web/dataset/call_kw/hr.department/get_views HTTP/1.1" 200 - 4 0.004 0.014
2025-07-27 11:47:20,992 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:20] "POST /web/dataset/call_kw/hr.department/web_search_read HTTP/1.1" 200 - 9 0.010 0.015
2025-07-27 11:47:27,124 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:27] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 7 0.010 0.018
2025-07-27 11:47:27,179 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:27] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 4 0.006 0.011
2025-07-27 11:47:27,261 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:27] "POST /web/dataset/call_kw/hr.division/onchange HTTP/1.1" 200 - 10 0.014 0.034
2025-07-27 11:47:31,376 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:31] "POST /web/dataset/call_kw/hr.division/write HTTP/1.1" 200 - 9 0.022 0.033
2025-07-27 11:47:31,427 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:31] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 11 0.014 0.015
2025-07-27 11:47:31,460 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:31] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 7 0.004 0.013
2025-07-27 11:47:31,487 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:31] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 4 0.003 0.008
2025-07-27 11:47:34,257 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:34] "POST /web/dataset/call_button HTTP/1.1" 200 - 6 0.009 0.019
2025-07-27 11:47:34,361 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:34] "POST /web/dataset/call_kw/hr.department/web_search_read HTTP/1.1" 200 - 9 0.014 0.019
2025-07-27 11:47:36,414 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:36] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 11 0.018 0.018
2025-07-27 11:47:36,453 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:36] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 7 0.009 0.013
2025-07-27 11:47:36,481 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:36] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 4 0.003 0.009
2025-07-27 11:47:51,518 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:51] "POST /web/action/load HTTP/1.1" 200 - 13 0.019 0.041
2025-07-27 11:47:51,583 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:51] "POST /web/dataset/call_kw/hr.department/get_views HTTP/1.1" 200 - 8 0.013 0.028
2025-07-27 11:47:51,627 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:51] "POST /web/dataset/call_kw/hr.department/web_search_read HTTP/1.1" 200 - 9 0.011 0.016
2025-07-27 11:47:53,883 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:53] "POST /web/dataset/call_kw/hr.department/read HTTP/1.1" 200 - 11 0.021 0.020
2025-07-27 11:47:53,962 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:53] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.019 0.015
2025-07-27 11:47:54,009 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:47:54] "POST /mail/thread/messages HTTP/1.1" 200 - 28 0.063 0.020
2025-07-27 11:48:08,388 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:08] "POST /web/dataset/call_kw/hr.department/name_search HTTP/1.1" 200 - 5 0.008 0.025
2025-07-27 11:48:10,245 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:10] "POST /web/dataset/call_kw/hr.department/web_search_read HTTP/1.1" 200 - 9 0.009 0.012
2025-07-27 11:48:14,023 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:14] "POST /web/action/load HTTP/1.1" 200 - 12 0.022 0.033
2025-07-27 11:48:14,112 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:14] "POST /web/dataset/call_kw/hr.unit/get_views HTTP/1.1" 200 - 14 0.022 0.036
2025-07-27 11:48:14,180 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:14] "POST /web/dataset/call_kw/hr.unit/web_search_read HTTP/1.1" 200 - 11 0.019 0.019
2025-07-27 11:48:14,233 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:14] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.004 0.011
2025-07-27 11:48:15,801 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:15] "POST /web/dataset/call_kw/hr.unit/onchange HTTP/1.1" 200 - 4 0.004 0.012
2025-07-27 11:48:21,377 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:21] "POST /web/dataset/call_kw/hr.department/name_search HTTP/1.1" 200 - 5 0.008 0.013
2025-07-27 11:48:24,591 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:24] "POST /web/dataset/call_kw/hr.unit/onchange HTTP/1.1" 200 - 5 0.005 0.016
2025-07-27 11:48:24,628 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:24] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 11:48:28,280 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:28] "POST /web/dataset/call_kw/hr.employee/name_search HTTP/1.1" 200 - 5 0.007 0.020
2025-07-27 11:48:48,251 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:48] "POST /web/dataset/call_kw/hr.unit/web_search_read HTTP/1.1" 200 - 11 0.033 0.012
2025-07-27 11:48:48,280 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:48] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.003 0.008
2025-07-27 11:48:50,501 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:50] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.012 0.012
2025-07-27 11:48:50,545 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:50] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.037 0.028
2025-07-27 11:48:54,645 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:54] "POST /web/dataset/call_button HTTP/1.1" 200 - 22 0.067 0.068
2025-07-27 11:48:54,718 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:54] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.024 0.023
2025-07-27 11:48:59,703 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:59] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.039 0.033
2025-07-27 11:48:59,770 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:59] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.016 0.014
2025-07-27 11:48:59,788 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:48:59] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.033 0.017
2025-07-27 11:49:12,931 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:12] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.024
2025-07-27 11:49:12,961 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:12] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.023 0.031
2025-07-27 11:49:15,213 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /web/dataset/call_kw/hr.job/onchange HTTP/1.1" 200 - 4 0.004 0.022
2025-07-27 11:49:15,244 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.022
2025-07-27 11:49:15,278 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.033 0.029
2025-07-27 11:49:15,331 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.024 0.026
2025-07-27 11:49:15,434 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.006 0.009
2025-07-27 11:49:15,458 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.016 0.018
2025-07-27 11:49:15,477 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.022 0.019
2025-07-27 11:49:15,552 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /web/dataset/call_kw/hr.job/write HTTP/1.1" 200 - 9 0.049 0.051
2025-07-27 11:49:15,610 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.020 0.025
2025-07-27 11:49:15,662 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.015 0.015
2025-07-27 11:49:15,696 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.033 0.026
2025-07-27 11:49:15,707 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:49:15] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.034 0.029
2025-07-27 11:50:11,450 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:11] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.031 0.018
2025-07-27 11:50:11,457 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:11] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.025
2025-07-27 11:50:12,861 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:12] "POST /web/dataset/call_kw/hr.job/onchange HTTP/1.1" 200 - 4 0.010 0.038
2025-07-27 11:50:12,884 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:12] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.024 0.042
2025-07-27 11:50:12,920 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:12] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.035 0.059
2025-07-27 11:50:12,991 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:12] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.028 0.026
2025-07-27 11:50:13,080 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:13] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.010 0.010
2025-07-27 11:50:13,094 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:13] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.015 0.016
2025-07-27 11:50:13,102 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:13] "POST /web/dataset/call_kw/hr.job/write HTTP/1.1" 200 - 8 0.014 0.015
2025-07-27 11:50:13,170 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:13] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.022 0.024
2025-07-27 11:50:13,199 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:13] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.007 0.007
2025-07-27 11:50:13,213 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:13] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.015 0.012
2025-07-27 11:50:13,228 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:13] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.009 0.024
2025-07-27 11:50:16,794 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.015 0.016
2025-07-27 11:50:16,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:16] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.028 0.027
2025-07-27 11:50:18,429 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /web/dataset/call_kw/hr.job/onchange HTTP/1.1" 200 - 4 0.006 0.038
2025-07-27 11:50:18,442 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.025 0.012
2025-07-27 11:50:18,457 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.015 0.032
2025-07-27 11:50:18,490 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.014 0.013
2025-07-27 11:50:18,657 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.018 0.022
2025-07-27 11:50:18,681 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /web/dataset/call_kw/hr.job/write HTTP/1.1" 200 - 8 0.028 0.027
2025-07-27 11:50:18,691 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.035 0.034
2025-07-27 11:50:18,777 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.027 0.031
2025-07-27 11:50:18,812 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.005 0.011
2025-07-27 11:50:18,833 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.023 0.012
2025-07-27 11:50:18,847 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:18] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.016 0.030
2025-07-27 11:50:20,497 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:20] "GET /web HTTP/1.1" 200 - 17 0.036 0.089
2025-07-27 11:50:20,907 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:20] "GET /web/webclient/load_menus/f855969452d5e4918fa4816d22948eaf11cb3041ba0dba1b5590331945ce61cd HTTP/1.1" 200 - 3 0.005 0.025
2025-07-27 11:50:20,907 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:20] "GET /web/webclient/translations/d79ae96871618198c190218bdf3011f4ff6f8d28?lang=en_US HTTP/1.1" 200 - 3 0.008 0.022
2025-07-27 11:50:21,607 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:21] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.046
2025-07-27 11:50:21,759 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:21] "POST /web/action/load HTTP/1.1" 200 - 13 0.028 0.029
2025-07-27 11:50:21,925 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:21] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.093 0.058
2025-07-27 11:50:22,000 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.028 0.042
2025-07-27 11:50:22,031 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.015 0.030
2025-07-27 11:50:22,041 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.026 0.019
2025-07-27 11:50:22,058 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.015 0.062
2025-07-27 11:50:22,135 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.019 0.027
2025-07-27 11:50:22,234 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.006 0.020
2025-07-27 11:50:22,248 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 3 0.000 0.014
2025-07-27 11:50:22,253 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.031 0.016
2025-07-27 11:50:22,429 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.014 0.017
2025-07-27 11:50:22,437 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:22] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.013 0.018
2025-07-27 11:50:25,692 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:25] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.005 0.019
2025-07-27 11:50:25,718 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:25] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.019
2025-07-27 11:50:27,477 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /web/dataset/call_kw/hr.job/onchange HTTP/1.1" 200 - 4 0.008 0.037
2025-07-27 11:50:27,490 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.035
2025-07-27 11:50:27,529 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.037 0.034
2025-07-27 11:50:27,586 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.019
2025-07-27 11:50:27,679 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.009 0.012
2025-07-27 11:50:27,724 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /web/dataset/call_kw/hr.job/write HTTP/1.1" 200 - 8 0.010 0.021
2025-07-27 11:50:27,734 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.016 0.025
2025-07-27 11:50:27,784 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.014 0.031
2025-07-27 11:50:27,810 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.014 0.003
2025-07-27 11:50:27,831 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.008 0.029
2025-07-27 11:50:27,831 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:27] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.020 0.010
2025-07-27 11:50:32,059 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:32] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.005 0.009
2025-07-27 11:50:32,093 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:32] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.024 0.014
2025-07-27 11:50:33,381 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:33] "POST /web/action/load HTTP/1.1" 200 - 13 0.025 0.043
2025-07-27 11:50:33,437 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:33] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 5 0.008 0.022
2025-07-27 11:50:33,527 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:33] "POST /web/dataset/call_kw/hr.job/onchange HTTP/1.1" 200 - 5 0.015 0.016
2025-07-27 11:50:35,710 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:35] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.013 0.034
2025-07-27 11:50:38,089 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:38] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.009
2025-07-27 11:50:38,106 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:38] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 12 0.014 0.017
2025-07-27 11:50:39,895 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:39] "POST /web/dataset/call_kw/hr.job/onchange HTTP/1.1" 200 - 9 0.012 0.033
2025-07-27 11:50:39,993 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:39] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.003 0.021
2025-07-27 11:50:50,500 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:50] "POST /web/dataset/call_kw/hr.job/onchange HTTP/1.1" 200 - 4 0.006 0.018
2025-07-27 11:50:50,650 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:50] "POST /web/dataset/call_kw/website/name_search HTTP/1.1" 200 - 5 0.006 0.025
2025-07-27 11:50:58,470 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:50:58] "POST /web/dataset/call_kw/hr.employee/name_search HTTP/1.1" 200 - 5 0.004 0.011
2025-07-27 11:51:03,392 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:51:03] "POST /web/dataset/call_kw/hr.job.category/name_search HTTP/1.1" 200 - 5 0.007 0.011
2025-07-27 11:51:06,403 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:51:06] "POST /web/dataset/call_kw/hr.job.grade/name_search HTTP/1.1" 200 - 5 0.003 0.011
2025-07-27 11:51:39,634 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:51:39] "POST /web/dataset/call_kw/hr.job.equipment/onchange HTTP/1.1" 200 - 4 0.011 0.007
2025-07-27 11:51:46,932 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:51:46] "POST /web/dataset/call_kw/hr.job.access/onchange HTTP/1.1" 200 - 4 0.020 0.009
2025-07-27 11:51:50,649 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:51:50] "POST /web/dataset/call_kw/hr.job.access/onchange HTTP/1.1" 200 - 3 0.004 0.009
2025-07-27 11:52:00,970 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:00] "POST /web/dataset/call_kw/hr.job.access/onchange HTTP/1.1" 200 - 3 0.002 0.015
2025-07-27 11:52:20,593 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /web/dataset/call_kw/hr.job/create HTTP/1.1" 200 - 36 0.155 0.150
2025-07-27 11:52:20,666 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 18 0.026 0.028
2025-07-27 11:52:20,748 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.009 0.015
2025-07-27 11:52:20,769 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.041 0.040
2025-07-27 11:52:20,785 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.039 0.029
2025-07-27 11:52:20,879 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.015 0.025
2025-07-27 11:52:20,922 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.008 0.010
2025-07-27 11:52:20,928 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.006 0.013
2025-07-27 11:52:20,942 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:20] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.024 0.017
2025-07-27 11:52:43,348 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:43] "POST /web/action/load HTTP/1.1" 200 - 11 0.017 0.029
2025-07-27 11:52:43,462 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:43] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.007 0.047
2025-07-27 11:52:43,519 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:43] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 4 0.004 0.011
2025-07-27 11:52:45,145 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:45] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 9 0.016 0.025
2025-07-27 11:52:49,806 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:49] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.013 0.041
2025-07-27 11:52:49,882 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:49] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.009 0.018
2025-07-27 11:52:49,914 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:49] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.028 0.026
2025-07-27 11:52:51,830 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:52:51] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.015 0.021
2025-07-27 11:53:02,519 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:02] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.025 0.037
2025-07-27 11:53:02,620 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:02] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.019
2025-07-27 11:53:02,624 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:02] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.013 0.016
2025-07-27 11:53:15,056 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:15] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.015 0.025
2025-07-27 11:53:15,074 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:15] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.027 0.027
2025-07-27 11:53:15,098 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:15] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.017 0.024
2025-07-27 11:53:15,134 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:15] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.045 0.044
2025-07-27 11:53:15,238 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:15] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.023 0.028
2025-07-27 11:53:15,269 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:15] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.007 0.005
2025-07-27 11:53:15,273 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:15] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.007 0.013
2025-07-27 11:53:15,286 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:15] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.022 0.011
2025-07-27 11:53:18,792 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.028 0.020
2025-07-27 11:53:18,797 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:18] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.029 0.021
2025-07-27 11:53:18,830 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:18] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.012 0.052
2025-07-27 11:53:18,866 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:18] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.052 0.063
2025-07-27 11:53:18,963 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:18] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.021 0.030
2025-07-27 11:53:19,022 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:19] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.010 0.013
2025-07-27 11:53:19,025 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:19] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.014 0.009
2025-07-27 11:53:19,042 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:53:19] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.027 0.022
2025-07-27 11:54:34,901 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:34] "POST /web/action/load HTTP/1.1" 200 - 11 0.017 0.034
2025-07-27 11:54:34,941 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:34] "POST /report/check_wkhtmltopdf HTTP/1.1" 200 - 3 0.004 0.007
2025-07-27 11:54:35,235 140048 INFO ardano_hr2 odoo.addons.base.models.ir_actions_report: Generating PDF on Windows platform require DPI >= 96. Using 96 instead. 
2025-07-27 11:54:38,439 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:38] "GET /web/assets/763-5307707/1/web.report_assets_pdf.min.css HTTP/1.1" 200 - 5 0.004 0.045
2025-07-27 11:54:38,444 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:38] "GET /web/assets/761-ebdf73e/1/web.report_assets_common.min.css HTTP/1.1" 200 - 5 0.005 0.044
2025-07-27 11:54:38,583 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:38] "GET / HTTP/1.1" 200 - 23 0.021 0.060
2025-07-27 11:54:42,508 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:42] "GET /web/static/fonts/lato/Lato-Bla-webfont.woff HTTP/1.1" 200 - 0 0.000 0.038
2025-07-27 11:54:42,513 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:42] "GET /web/static/fonts/lato/Lato-Lig-webfont.woff HTTP/1.1" 200 - 0 0.000 0.046
2025-07-27 11:54:42,517 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:42] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.048
2025-07-27 11:54:42,517 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:42] "GET /web/static/fonts/lato/Lato-Hai-webfont.woff HTTP/1.1" 200 - 0 0.000 0.050
2025-07-27 11:54:42,519 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:42] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.052
2025-07-27 11:54:44,161 140048 INFO ardano_hr2 odoo.addons.base.models.ir_actions_report: The PDF report has been generated for model: hr.job, records [8]. 
2025-07-27 11:54:44,168 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:44] "POST /report/download HTTP/1.1" 200 - 40 0.113 9.082
2025-07-27 11:54:44,281 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:44] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.025 0.022
2025-07-27 11:54:44,357 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:44] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.006
2025-07-27 11:54:44,359 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:54:44] "POST /mail/thread/data HTTP/1.1" 200 - 7 0.004 0.012
2025-07-27 11:55:33,220 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:33] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.019 0.024
2025-07-27 11:55:35,653 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:35] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.013 0.020
2025-07-27 11:55:35,693 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:35] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.038 0.031
2025-07-27 11:55:41,567 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:41] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.019 0.023
2025-07-27 11:55:41,613 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:41] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.006 0.007
2025-07-27 11:55:41,628 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:41] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.011 0.016
2025-07-27 11:55:46,332 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:46] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.020 0.021
2025-07-27 11:55:46,391 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:46] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.051 0.042
2025-07-27 11:55:46,420 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:46] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.041 0.045
2025-07-27 11:55:46,463 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:46] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.095 0.062
2025-07-27 11:55:46,540 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:46] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 20 0.021 0.028
2025-07-27 11:55:46,607 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:46] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.022 0.020
2025-07-27 11:55:46,640 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:46] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.040 0.023
2025-07-27 11:55:46,642 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:46] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.052 0.025
2025-07-27 11:55:47,280 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:47] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.013
2025-07-27 11:55:47,317 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:47] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.029 0.023
2025-07-27 11:55:47,337 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:47] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.032 0.026
2025-07-27 11:55:47,379 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:47] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.059 0.049
2025-07-27 11:55:47,471 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:47] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 20 0.026 0.032
2025-07-27 11:55:47,541 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:47] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.026 0.018
2025-07-27 11:55:47,543 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:47] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.027 0.014
2025-07-27 11:55:47,550 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:47] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.028 0.030
2025-07-27 11:55:48,131 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:48] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.019
2025-07-27 11:55:48,176 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:48] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.033 0.039
2025-07-27 11:55:48,207 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:48] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.038 0.031
2025-07-27 11:55:48,239 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:48] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.067 0.043
2025-07-27 11:55:48,293 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:48] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 20 0.018 0.020
2025-07-27 11:55:48,350 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:48] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.021 0.019
2025-07-27 11:55:48,351 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:48] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.025 0.014
2025-07-27 11:55:48,359 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:48] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.028 0.023
2025-07-27 11:55:49,059 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:49] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.021 0.017
2025-07-27 11:55:49,098 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:49] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.030 0.044
2025-07-27 11:55:49,142 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:49] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.036 0.054
2025-07-27 11:55:49,154 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:49] "POST /web/dataset/call_button HTTP/1.1" 200 - 14 0.056 0.060
2025-07-27 11:55:49,268 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:49] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 19 0.018 0.018
2025-07-27 11:55:49,347 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:49] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.022 0.016
2025-07-27 11:55:49,353 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:49] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.024 0.018
2025-07-27 11:55:49,365 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:49] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.040 0.020
2025-07-27 11:55:50,327 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.014 0.011
2025-07-27 11:55:50,350 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.028 0.019
2025-07-27 11:55:50,381 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.034 0.028
2025-07-27 11:55:50,430 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.059 0.057
2025-07-27 11:55:50,510 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 20 0.022 0.032
2025-07-27 11:55:50,597 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.017 0.016
2025-07-27 11:55:50,603 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.032 0.013
2025-07-27 11:55:50,610 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.033 0.023
2025-07-27 11:55:50,800 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.010 0.020
2025-07-27 11:55:50,842 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.027 0.028
2025-07-27 11:55:50,850 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.031 0.023
2025-07-27 11:55:50,864 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.041 0.031
2025-07-27 11:55:50,923 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 20 0.017 0.024
2025-07-27 11:55:50,994 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:50] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.022 0.026
2025-07-27 11:55:51,007 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:51] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.034 0.024
2025-07-27 11:55:51,022 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:51] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.057 0.021
2025-07-27 11:55:52,278 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:52] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.019
2025-07-27 11:55:52,301 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:52] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.028 0.024
2025-07-27 11:55:52,339 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:52] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.029 0.038
2025-07-27 11:55:52,365 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:52] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.045 0.053
2025-07-27 11:55:52,426 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:52] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 20 0.016 0.024
2025-07-27 11:55:52,469 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:52] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.012 0.012
2025-07-27 11:55:52,483 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:52] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.018 0.012
2025-07-27 11:55:52,489 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:52] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.030 0.016
2025-07-27 11:55:53,534 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:53] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.023 0.025
2025-07-27 11:55:53,566 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:53] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.011 0.058
2025-07-27 11:55:53,594 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:53] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.062 0.040
2025-07-27 11:55:53,614 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:53] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.052 0.059
2025-07-27 11:55:53,665 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:53] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.005 0.019
2025-07-27 11:55:53,771 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:55:53] "POST /web/dataset/call_kw/hr.jva.form/onchange HTTP/1.1" 200 - 15 0.022 0.034
2025-07-27 11:56:30,686 140048 WARNING ardano_hr2 odoo.http: Number of requested positions must be at least 1. 
2025-07-27 11:56:30,687 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:30] "POST /web/dataset/call_kw/hr.jva.form/create HTTP/1.1" 200 - 10 0.020 0.329
2025-07-27 11:56:39,202 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:39] "POST /web/dataset/call_kw/hr.jva.form/onchange HTTP/1.1" 200 - 3 0.008 0.011
2025-07-27 11:56:41,205 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:41] "POST /web/dataset/call_kw/hr.jva.form/create HTTP/1.1" 200 - 27 0.057 0.058
2025-07-27 11:56:41,255 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:41] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 8 0.007 0.021
2025-07-27 11:56:41,374 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:41] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.031 0.030
2025-07-27 11:56:41,393 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:41] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.064 0.025
2025-07-27 11:56:41,396 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:41] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.040 0.036
2025-07-27 11:56:43,776 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:43] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.029 0.015
2025-07-27 11:56:43,806 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:43] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.043 0.025
2025-07-27 11:56:43,826 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:43] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.032 0.048
2025-07-27 11:56:43,924 140048 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.requested_by.work_email or user.email)|safe} as fallback 
2025-07-27 11:56:43,924 140048 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.requested_by.work_email or user.email)|safe} as fallback 
2025-07-27 11:56:48,093 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:48] "POST /web/dataset/call_button HTTP/1.1" 200 - 31 0.114 4.237
2025-07-27 11:56:48,193 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:48] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.017 0.015
2025-07-27 11:56:48,225 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:56:48] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.046 0.022
2025-07-27 11:57:04,484 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:04] "POST /web/action/load HTTP/1.1" 200 - 12 0.019 0.023
2025-07-27 11:57:04,724 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:04] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 25 0.052 0.167
2025-07-27 11:57:04,977 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:04] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 89 0.072 0.077
2025-07-27 11:57:05,021 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:05] "POST /web/dataset/call_kw/res.lang/read HTTP/1.1" 200 - 4 0.002 0.008
2025-07-27 11:57:05,207 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:05] "POST /base_setup/demo_active HTTP/1.1" 200 - 5 0.006 0.008
2025-07-27 11:57:05,258 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:05] "POST /base_setup/data HTTP/1.1" 200 - 8 0.026 0.012
2025-07-27 11:57:10,124 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:10] "POST /web/action/load HTTP/1.1" 200 - 12 0.012 0.023
2025-07-27 11:57:10,180 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:10] "POST /web/dataset/call_kw/mail.mail/get_views HTTP/1.1" 200 - 4 0.003 0.026
2025-07-27 11:57:10,231 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:10] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.008
2025-07-27 11:57:10,264 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:10] "POST /web/dataset/call_kw/mail.mail/web_search_read HTTP/1.1" 200 - 8 0.016 0.027
2025-07-27 11:57:13,976 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:13] "POST /web/dataset/call_kw/mail.mail/read HTTP/1.1" 200 - 8 0.025 0.022
2025-07-27 11:57:14,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:14] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 3 0.002 0.008
2025-07-27 11:57:30,732 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:30] "GET /web HTTP/1.1" 200 - 17 0.031 0.043
2025-07-27 11:57:31,974 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:31] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.107 0.064
2025-07-27 11:57:32,067 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:32] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 8 0.012 0.024
2025-07-27 11:57:32,082 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:32] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.008 0.023
2025-07-27 11:57:32,109 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:32] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.085 0.027
2025-07-27 11:57:32,120 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:32] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.009 0.013
2025-07-27 11:57:46,316 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:46] "GET /web HTTP/1.1" 200 - 17 0.017 0.029
2025-07-27 11:57:47,492 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.096 0.049
2025-07-27 11:57:47,542 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.025 0.024
2025-07-27 11:57:47,553 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.018 0.020
2025-07-27 11:57:47,557 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.012 0.025
2025-07-27 11:57:47,591 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.013 0.012
2025-07-27 11:57:47,635 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 8 0.004 0.015
2025-07-27 11:57:47,720 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.013 0.012
2025-07-27 11:57:47,732 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.020 0.019
2025-07-27 11:57:47,843 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:47] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 200 - 8 0.006 0.011
2025-07-27 11:57:52,556 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:52] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.019
2025-07-27 11:57:52,587 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:52] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.036 0.024
2025-07-27 11:57:52,628 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:52] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.045 0.043
2025-07-27 11:57:52,723 140048 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.approved_by.work_email or user.email)|safe} as fallback 
2025-07-27 11:57:52,724 140048 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using ${(object.approved_by.work_email or user.email)|safe} as fallback 
2025-07-27 11:57:56,886 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:56] "POST /web/dataset/call_button HTTP/1.1" 200 - 33 0.118 4.237
2025-07-27 11:57:57,000 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:57] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.016 0.017
2025-07-27 11:57:57,042 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:57:57] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.050 0.027
2025-07-27 11:58:08,295 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:08] "GET /web HTTP/1.1" 200 - 17 0.024 0.044
2025-07-27 11:58:09,546 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:09] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.030 0.036
2025-07-27 11:58:09,616 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:09] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.007 0.022
2025-07-27 11:58:09,635 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:09] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.008 0.030
2025-07-27 11:58:09,648 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:09] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.057 0.026
2025-07-27 11:58:09,680 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:09] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.015 0.013
2025-07-27 11:58:09,743 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:09] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 10 0.008 0.019
2025-07-27 11:58:09,872 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:09] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.016 0.015
2025-07-27 11:58:09,903 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:09] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.026 0.039
2025-07-27 11:58:10,023 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:10] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.008 0.014
2025-07-27 11:58:12,761 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:12] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.009 0.009
2025-07-27 11:58:12,780 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:12] "POST /web/dataset/call_button HTTP/1.1" 200 - 7 0.012 0.019
2025-07-27 11:58:12,786 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:12] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.025 0.015
2025-07-27 11:58:12,805 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:12] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.022 0.030
2025-07-27 11:58:12,983 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:12] "POST /web/dataset/call_kw/hr.jva.form/read HTTP/1.1" 200 - 10 0.021 0.018
2025-07-27 11:58:12,992 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:12] "GET /jobs/detail/2 HTTP/1.1" 301 - 7 0.023 0.023
2025-07-27 11:58:12,997 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:12] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.026 0.022
2025-07-27 11:58:13,012 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:13] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.038 0.024
2025-07-27 11:58:13,085 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:13] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.040 0.020
2025-07-27 11:58:13,109 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:13] "GET /jobs/detail/chief-technical-officer-2 HTTP/1.1" 200 - 25 0.032 0.057
2025-07-27 11:58:13,869 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:13] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.006 0.009
2025-07-27 11:58:42,130 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:42] "GET /jobs HTTP/1.1" 200 - 13 0.021 0.113
2025-07-27 11:58:58,784 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:58] "GET /jobs/apply/chief-technical-officer-2 HTTP/1.1" 200 - 26 0.023 0.066
2025-07-27 11:58:59,484 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:59] "POST /web/dataset/call_kw/res.users/read HTTP/1.1" 200 - 5 0.005 0.023
2025-07-27 11:58:59,509 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 11:58:59] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.005 0.006
2025-07-27 12:02:34,847 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:34] "POST /website/form/hr.applicant HTTP/1.1" 200 - 56 0.143 0.238
2025-07-27 12:02:34,858 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 12:02:34,866 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 12:02:35,001 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:35] "GET /job-thank-you HTTP/1.1" 200 - 30 0.025 0.097
2025-07-27 12:02:35,496 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:35] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.004 0.007
2025-07-27 12:02:38,571 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.005 0.013
2025-07-27 12:02:38,573 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.016
2025-07-27 12:02:38,596 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.020 0.028
2025-07-27 12:02:38,614 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.025 0.031
2025-07-27 12:02:38,688 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 8 0.017 0.052
2025-07-27 12:02:38,769 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.004 0.011
2025-07-27 12:02:38,804 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.006 0.011
2025-07-27 12:02:38,815 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.018 0.008
2025-07-27 12:02:38,844 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.003 0.010
2025-07-27 12:02:38,873 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:38] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.016 0.028
2025-07-27 12:02:39,000 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:39] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.007 0.016
2025-07-27 12:02:44,122 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.041 0.023
2025-07-27 12:02:44,169 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.006 0.012
2025-07-27 12:02:44,292 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.017 0.023
2025-07-27 12:02:44,297 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 3 0.003 0.017
2025-07-27 12:02:44,300 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.036 0.016
2025-07-27 12:02:44,388 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.016 0.044
2025-07-27 12:02:44,435 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "POST /web/dataset/call_kw/ir.attachment/register_as_main_attachment HTTP/1.1" 200 - 20 0.021 0.094
2025-07-27 12:02:44,452 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 12:02:44,463 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.010 0.015
2025-07-27 12:02:44,491 140048 INFO ardano_hr2 odoo.addons.iap.tools.iap_tools: iap jsonrpc https://iap.odoo.com/iap/1/balance 
2025-07-27 12:02:44,525 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "GET /web/image/765/1920x160 HTTP/1.1" 200 - 8 0.024 0.025
2025-07-27 12:02:44,556 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "GET /web/image/765?unique=1 HTTP/1.1" 200 - 8 0.037 0.041
2025-07-27 12:02:44,564 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "GET /web/image/764/1920x160 HTTP/1.1" 200 - 8 0.033 0.050
2025-07-27 12:02:44,682 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:02:44] "POST /web/dataset/call_kw/ir.attachment/register_as_main_attachment HTTP/1.1" 200 - 16 0.018 0.109
2025-07-27 12:02:46,347 140048 INFO ardano_hr2 odoo.addons.iap.tools.iap_tools: iap jsonrpc https://iap-extract.odoo.com/api/extract/applicant/2/parse 
2025-07-27 12:02:47,518 140048 ERROR ardano_hr2 odoo.sql_db: bad query: UPDATE "hr_applicant" SET "extract_state" = 'not_enough_credit', "extract_status_code" = 3, "state_processed" = false, "write_date" = '2025-07-27 12:02:44.443123', "write_uid" = 1 WHERE id IN (28)
ERROR: could not serialize access due to concurrent update
 
2025-07-27 12:02:47,519 140048 ERROR ardano_hr2 odoo.addons.hr_recruitment_extract.models.hr_applicant: Couldn't upload hr.applicant with id 28: could not serialize access due to concurrent update
 
2025-07-27 12:02:47,519 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 12:02:47,534 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 12:02:47,550 140048 INFO ardano_hr2 odoo.addons.iap.tools.iap_tools: iap jsonrpc https://iap.odoo.com/iap/1/balance 
2025-07-27 12:02:48,766 140048 INFO ardano_hr2 odoo.addons.iap.tools.iap_tools: iap jsonrpc https://iap-extract.odoo.com/api/extract/applicant/2/parse 
2025-07-27 12:02:49,785 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 12:03:55,997 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:03:55] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.008
2025-07-27 12:03:56,027 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:03:56] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.022 0.027
2025-07-27 12:03:56,064 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:03:56] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.040 0.024
2025-07-27 12:03:56,101 140048 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 12:03:56,103 140048 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 12:04:00,482 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:04:00] "POST /web/dataset/call_button HTTP/1.1" 200 - 38 0.114 4.382
2025-07-27 12:04:00,504 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 12:04:00,519 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 12:04:00,580 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:04:00] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 19 0.027 0.026
2025-07-27 12:04:00,662 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:04:00] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.029 0.020
2025-07-27 12:04:00,673 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:04:00] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.041 0.023
2025-07-27 12:04:00,683 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:04:00] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.034 0.026
2025-07-27 12:06:15,565 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:15] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.009 0.012
2025-07-27 12:06:15,586 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:15] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.021 0.019
2025-07-27 12:06:15,602 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:15] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.025 0.026
2025-07-27 12:06:15,609 140048 WARNING ardano_hr2 odoo.http: Only the assigned Technical Interviewer can perform this action. 
2025-07-27 12:06:15,610 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:15] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.006 0.057
2025-07-27 12:06:15,719 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:15] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.007 0.009
2025-07-27 12:06:15,737 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:15] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.014 0.019
2025-07-27 12:06:58,641 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:58] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.010 0.012
2025-07-27 12:06:58,652 140048 WARNING ardano_hr2 odoo.http: Only the assigned Technical Interviewer can perform this action. 
2025-07-27 12:06:58,653 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:58] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.006 0.017
2025-07-27 12:06:58,668 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:58] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.027 0.020
2025-07-27 12:06:58,736 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:58] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.031 0.045
2025-07-27 12:06:58,838 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:58] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.012 0.012
2025-07-27 12:06:58,858 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:06:58] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.025 0.018
2025-07-27 12:07:05,491 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:05] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.021 0.018
2025-07-27 12:07:05,507 140048 WARNING ardano_hr2 odoo.http: Only the assigned Technical Interviewer can perform this action. 
2025-07-27 12:07:05,510 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:05] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.011 0.039
2025-07-27 12:07:05,551 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:05] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.054 0.039
2025-07-27 12:07:05,580 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:05] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.042 0.056
2025-07-27 12:07:05,836 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:05] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.005 0.017
2025-07-27 12:07:05,872 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:05] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.028 0.024
2025-07-27 12:07:14,366 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:14] "GET /web HTTP/1.1" 200 - 17 0.019 0.043
2025-07-27 12:07:15,534 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.055 0.044
2025-07-27 12:07:15,654 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.015 0.020
2025-07-27 12:07:15,676 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.071 0.033
2025-07-27 12:07:15,677 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 4 0.009 0.045
2025-07-27 12:07:15,705 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.017 0.011
2025-07-27 12:07:15,770 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 19 0.020 0.021
2025-07-27 12:07:15,794 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.003 0.007
2025-07-27 12:07:15,915 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 3 0.005 0.013
2025-07-27 12:07:15,952 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.029 0.030
2025-07-27 12:07:15,978 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:15] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.054 0.032
2025-07-27 12:07:16,003 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:16] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.023 0.031
2025-07-27 12:07:16,104 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.006 0.012
2025-07-27 12:07:16,126 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:16] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.018 0.020
2025-07-27 12:07:16,219 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:16] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.019 0.032
2025-07-27 12:07:16,249 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:16] "GET /web/image/765/1920x160 HTTP/1.1" 304 - 8 0.025 0.044
2025-07-27 12:07:16,256 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:16] "GET /web/image/764/1920x160 HTTP/1.1" 304 - 8 0.019 0.048
2025-07-27 12:07:16,274 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:16] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.028 0.049
2025-07-27 12:07:16,275 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:07:16] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.023 0.053
2025-07-27 12:08:09,926 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:09] "POST /web/action/load HTTP/1.1" 200 - 11 0.016 0.008
2025-07-27 12:08:10,004 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:10] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 3 0.005 0.041
2025-07-27 12:08:10,117 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:10] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 26 0.030 0.042
2025-07-27 12:08:10,155 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:10] "POST /web/dataset/call_kw/res.lang/read HTTP/1.1" 200 - 4 0.000 0.013
2025-07-27 12:08:10,267 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:10] "POST /base_setup/demo_active HTTP/1.1" 200 - 4 0.005 0.000
2025-07-27 12:08:10,282 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:10] "POST /base_setup/data HTTP/1.1" 200 - 7 0.005 0.005
2025-07-27 12:08:23,271 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:23] "POST /web/action/load HTTP/1.1" 200 - 15 0.018 0.030
2025-07-27 12:08:23,523 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:23] "POST /web/dataset/call_kw/ir.cron/get_views HTTP/1.1" 200 - 54 0.070 0.142
2025-07-27 12:08:23,576 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:23] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.000 0.008
2025-07-27 12:08:23,601 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:23] "POST /web/dataset/call_kw/ir.cron/web_search_read HTTP/1.1" 200 - 11 0.004 0.030
2025-07-27 12:08:31,163 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:31] "GET /web HTTP/1.1" 200 - 17 0.013 0.038
2025-07-27 12:08:32,384 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:32] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.047 0.053
2025-07-27 12:08:32,499 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:32] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.039 0.024
2025-07-27 12:08:32,515 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:32] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.018 0.023
2025-07-27 12:08:32,696 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:32] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.016 0.013
2025-07-27 12:08:39,556 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:39] "POST /web/action/load HTTP/1.1" 200 - 13 0.026 0.018
2025-07-27 12:08:39,614 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:39] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.007 0.027
2025-07-27 12:08:39,675 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:39] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.011 0.008
2025-07-27 12:08:39,700 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:08:39] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.016 0.025
2025-07-27 12:09:22,414 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:22] "POST /web/dataset/call_kw/mail.mail/get_views HTTP/1.1" 200 - 4 0.007 0.025
2025-07-27 12:09:22,520 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:22] "POST /web/dataset/call_kw/mail.mail/web_search_read HTTP/1.1" 200 - 8 0.010 0.052
2025-07-27 12:09:30,098 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:30] "POST /web/action/load HTTP/1.1" 200 - 13 0.009 0.018
2025-07-27 12:09:30,172 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:30] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.013 0.035
2025-07-27 12:09:30,247 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:30] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.011 0.006
2025-07-27 12:09:30,281 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:30] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.021 0.026
2025-07-27 12:09:36,380 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:36] "POST /web/action/load HTTP/1.1" 200 - 12 0.012 0.020
2025-07-27 12:09:36,438 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:36] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 4 0.005 0.031
2025-07-27 12:09:36,493 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:36] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.005 0.011
2025-07-27 12:09:36,566 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:36] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 24 0.025 0.062
2025-07-27 12:09:36,610 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:36] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.006 0.005
2025-07-27 12:09:37,044 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753618176643 HTTP/1.1" 200 - 6 0.014 0.020
2025-07-27 12:09:37,058 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753618176647 HTTP/1.1" 200 - 6 0.018 0.027
2025-07-27 12:09:37,063 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753618176647 HTTP/1.1" 200 - 6 0.013 0.032
2025-07-27 12:09:37,073 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753618176647 HTTP/1.1" 200 - 6 0.022 0.038
2025-07-27 12:09:37,078 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753618176648 HTTP/1.1" 200 - 6 0.018 0.040
2025-07-27 12:09:37,079 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753618176648 HTTP/1.1" 200 - 6 0.022 0.031
2025-07-27 12:09:37,110 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753618176648 HTTP/1.1" 200 - 6 0.019 0.029
2025-07-27 12:09:37,116 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753618176649 HTTP/1.1" 200 - 6 0.016 0.031
2025-07-27 12:09:37,119 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753618176649 HTTP/1.1" 200 - 6 0.020 0.024
2025-07-27 12:09:37,140 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753618176649 HTTP/1.1" 200 - 6 0.020 0.038
2025-07-27 12:09:37,142 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753618176649 HTTP/1.1" 200 - 6 0.019 0.037
2025-07-27 12:09:37,146 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753618176649 HTTP/1.1" 200 - 6 0.017 0.034
2025-07-27 12:09:37,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753618176651 HTTP/1.1" 200 - 6 0.012 0.031
2025-07-27 12:09:37,185 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753618176652 HTTP/1.1" 200 - 6 0.017 0.036
2025-07-27 12:09:37,189 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753618176652 HTTP/1.1" 200 - 6 0.028 0.026
2025-07-27 12:09:37,192 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753618176652 HTTP/1.1" 200 - 6 0.020 0.025
2025-07-27 12:09:37,221 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753618176653 HTTP/1.1" 200 - 6 0.013 0.044
2025-07-27 12:09:37,230 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753618176653 HTTP/1.1" 200 - 6 0.019 0.042
2025-07-27 12:09:37,230 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753618176653 HTTP/1.1" 200 - 6 0.012 0.041
2025-07-27 12:09:37,234 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:37] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753618176653 HTTP/1.1" 200 - 6 0.015 0.022
2025-07-27 12:09:39,470 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:39] "POST /web/action/load HTTP/1.1" 200 - 11 0.013 0.008
2025-07-27 12:09:39,517 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:39] "POST /web/dataset/call_kw/hr.jva.form/get_views HTTP/1.1" 200 - 4 0.004 0.025
2025-07-27 12:09:39,558 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:39] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 9 0.009 0.011
2025-07-27 12:09:44,134 140048 INFO ardano_hr2 odoo.models.unlink: User #2 deleted mail.message records with IDs: [442, 441, 439, 438, 437, 425, 424, 422, 421, 420, 416, 415, 413, 412, 411] 
2025-07-27 12:09:44,178 140048 INFO ardano_hr2 odoo.models.unlink: User #2 deleted hr.jva.form records with IDs: [8, 6, 4] 
2025-07-27 12:09:44,204 140048 INFO ardano_hr2 odoo.models.unlink: User #2 deleted mail.followers records with IDs: [164, 165, 166] 
2025-07-27 12:09:44,207 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:44] "POST /web/dataset/call_kw/hr.jva.form/unlink HTTP/1.1" 200 - 31 0.153 0.060
2025-07-27 12:09:44,238 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:44] "POST /web/dataset/call_kw/hr.jva.form/web_search_read HTTP/1.1" 200 - 4 0.002 0.010
2025-07-27 12:09:57,445 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:57] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.012 0.017
2025-07-27 12:09:57,475 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:09:57] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.027 0.029
2025-07-27 12:10:00,697 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:00] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.009 0.013
2025-07-27 12:10:00,825 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:00] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.035 0.036
2025-07-27 12:10:02,845 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:02] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.027 0.033
2025-07-27 12:10:15,481 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:15] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.009 0.016
2025-07-27 12:10:15,562 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:15] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.024 0.027
2025-07-27 12:10:17,210 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:17] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 5 0.007 0.012
2025-07-27 12:10:17,273 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:17] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 4 0.004 0.014
2025-07-27 12:10:18,253 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:18] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.010 0.027
2025-07-27 12:10:18,338 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:18] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 13 0.026 0.024
2025-07-27 12:10:19,752 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:19] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.009 0.017
2025-07-27 12:10:19,813 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:19] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 13 0.022 0.015
2025-07-27 12:10:21,108 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:21] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.010 0.018
2025-07-27 12:10:21,161 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:21] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.032 0.033
2025-07-27 12:10:22,937 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:22] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.005 0.013
2025-07-27 12:10:23,008 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:23] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 14 0.025 0.028
2025-07-27 12:10:43,699 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:43] "GET /web HTTP/1.1" 200 - 17 0.023 0.031
2025-07-27 12:10:44,778 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:44] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.044 0.044
2025-07-27 12:10:44,880 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:44] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.019 0.041
2025-07-27 12:10:44,901 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:44] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.017 0.021
2025-07-27 12:10:44,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:44] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.015 0.016
2025-07-27 12:10:46,369 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:46] "POST /web/action/load HTTP/1.1" 200 - 12 0.010 0.012
2025-07-27 12:10:46,416 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:46] "POST /web/dataset/call_kw/ir.cron/get_views HTTP/1.1" 200 - 6 0.011 0.027
2025-07-27 12:10:46,470 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:46] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.009
2025-07-27 12:10:46,491 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:46] "POST /web/dataset/call_kw/ir.cron/web_search_read HTTP/1.1" 200 - 9 0.011 0.023
2025-07-27 12:10:49,110 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:49] "POST /web/dataset/call_kw/ir.cron/web_search_read HTTP/1.1" 200 - 9 0.009 0.016
2025-07-27 12:10:51,019 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:51] "POST /web/dataset/call_kw/ir.cron/read HTTP/1.1" 200 - 15 0.052 0.025
2025-07-27 12:10:52,509 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Manually starting job `AlWasead: Check Staffing Levels`. 
2025-07-27 12:10:52,520 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `AlWasead: Check Staffing Levels` done. 
2025-07-27 12:10:52,529 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:52] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.013 0.017
2025-07-27 12:10:52,585 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:52] "POST /web/dataset/call_kw/ir.cron/read HTTP/1.1" 200 - 14 0.020 0.020
2025-07-27 12:10:58,502 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:58] "GET /web HTTP/1.1" 200 - 17 0.019 0.041
2025-07-27 12:10:59,422 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:59] "POST /web/action/load HTTP/1.1" 200 - 12 0.017 0.023
2025-07-27 12:10:59,551 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:59] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.053 0.041
2025-07-27 12:10:59,635 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:59] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 16 0.025 0.028
2025-07-27 12:10:59,657 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:59] "POST /web/dataset/call_kw/mail.mail/get_views HTTP/1.1" 200 - 4 0.005 0.028
2025-07-27 12:10:59,666 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:59] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.016 0.021
2025-07-27 12:10:59,696 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:59] "POST /mail/load_message_failures HTTP/1.1" 200 - 11 0.009 0.018
2025-07-27 12:10:59,723 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:59] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.002 0.006
2025-07-27 12:10:59,752 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:10:59] "POST /web/dataset/call_kw/mail.mail/web_search_read HTTP/1.1" 200 - 8 0.016 0.023
2025-07-27 12:11:05,048 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:11:05] "POST /web/dataset/call_kw/mail.mail/read HTTP/1.1" 200 - 8 0.017 0.020
2025-07-27 12:11:05,158 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:11:05] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 3 0.004 0.007
2025-07-27 12:12:34,066 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:34] "POST /web/action/load HTTP/1.1" 200 - 13 0.020 0.017
2025-07-27 12:12:34,132 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:34] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.011 0.030
2025-07-27 12:12:34,185 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:34] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.009 0.012
2025-07-27 12:12:34,241 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:34] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.028 0.036
2025-07-27 12:12:36,552 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:36] "POST /web/action/load HTTP/1.1" 200 - 13 0.013 0.027
2025-07-27 12:12:36,595 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:36] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 4 0.004 0.023
2025-07-27 12:12:36,649 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:36] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.004 0.009
2025-07-27 12:12:36,686 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:36] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.007 0.009
2025-07-27 12:12:36,691 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:36] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.010 0.013
2025-07-27 12:12:36,721 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:36] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.007 0.008
2025-07-27 12:12:36,741 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:36] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.016 0.020
2025-07-27 12:12:36,875 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:36] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.010 0.016
2025-07-27 12:12:38,030 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 19 0.018 0.028
2025-07-27 12:12:38,097 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.005 0.012
2025-07-27 12:12:38,244 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.024 0.021
2025-07-27 12:12:38,246 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 3 0.007 0.015
2025-07-27 12:12:38,258 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.036 0.025
2025-07-27 12:12:38,313 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.015 0.013
2025-07-27 12:12:38,584 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "GET /web/image/765/1920x160 HTTP/1.1" 304 - 8 0.027 0.036
2025-07-27 12:12:38,608 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "GET /web/image/764/1920x160 HTTP/1.1" 304 - 8 0.040 0.040
2025-07-27 12:12:38,615 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.036 0.046
2025-07-27 12:12:38,623 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:38] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.038 0.045
2025-07-27 12:12:51,807 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:51] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.050 0.019
2025-07-27 12:12:51,868 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:51] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.080 0.047
2025-07-27 12:12:51,954 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:51] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.118 0.050
2025-07-27 12:12:52,073 140048 INFO ardano_hr2 odoo.addons.phone_validation.tools.phone_validation: The `phonenumbers` Python module is not installed, contact numbers will not be verified. Please install the `phonenumbers` Python module. 
2025-07-27 12:12:52,087 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /web/dataset/call_button HTTP/1.1" 200 - 45 0.173 0.169
2025-07-27 12:12:52,250 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /web/dataset/call_kw/calendar.event/get_views HTTP/1.1" 200 - 43 0.053 0.089
2025-07-27 12:12:52,305 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /web/dataset/call_kw/calendar.event/check_access_rights HTTP/1.1" 200 - 3 0.001 0.012
2025-07-27 12:12:52,318 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.006 0.014
2025-07-27 12:12:52,338 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /appointment/appointment_type/get_staff_user_appointment_types HTTP/1.1" 200 - 6 0.026 0.017
2025-07-27 12:12:52,370 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /web/dataset/call_kw/calendar.filters/search_read HTTP/1.1" 200 - 7 0.019 0.012
2025-07-27 12:12:52,403 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /web/dataset/call_kw/calendar.event/search_read HTTP/1.1" 200 - 4 0.008 0.008
2025-07-27 12:12:52,432 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /web/dataset/call_kw/res.partner/get_attendee_detail HTTP/1.1" 200 - 3 0.004 0.006
2025-07-27 12:12:52,565 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "POST /onboarding/appointment HTTP/1.1" 200 - 24 0.043 0.068
2025-07-27 12:12:52,623 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "GET /web/image/onboarding.onboarding/1/panel_background_image HTTP/1.1" 304 - 7 0.007 0.009
2025-07-27 12:12:52,678 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "GET /web/image/res.partner/9/avatar_128 HTTP/1.1" 304 - 7 0.008 0.015
2025-07-27 12:12:52,680 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:52] "GET /web/image/res.partner/7/avatar_128 HTTP/1.1" 304 - 8 0.009 0.017
2025-07-27 12:12:56,135 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:56] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 20 0.030 0.033
2025-07-27 12:12:56,173 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:56] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.005 0.009
2025-07-27 12:12:56,247 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:56] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.012
2025-07-27 12:12:56,272 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:12:56] "POST /mail/thread/data HTTP/1.1" 200 - 19 0.020 0.024
2025-07-27 12:14:18,197 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:14:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.017 0.010
2025-07-27 12:14:18,250 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:14:18] "POST /mail/thread/data HTTP/1.1" 200 - 19 0.024 0.049
2025-07-27 12:18:56,518 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:18:56] "POST /web/action/load HTTP/1.1" 200 - 13 0.016 0.021
2025-07-27 12:18:56,602 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:18:56] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 7 0.008 0.034
2025-07-27 12:18:56,688 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:18:56] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.008 0.011
2025-07-27 12:18:56,731 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:18:56] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.022 0.027
2025-07-27 12:19:03,188 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:03] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 26 0.026 0.025
2025-07-27 12:19:03,294 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:03] "POST /mail/thread/data HTTP/1.1" 200 - 12 0.012 0.015
2025-07-27 12:19:03,301 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:03] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 3 0.005 0.011
2025-07-27 12:19:03,318 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:03] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.026 0.026
2025-07-27 12:19:03,465 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:03] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.008 0.016
2025-07-27 12:19:03,471 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:03] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.009 0.019
2025-07-27 12:19:03,481 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:03] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.014 0.017
2025-07-27 12:19:07,116 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:07] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.033 0.016
2025-07-27 12:19:11,001 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:11] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 20 0.021 0.027
2025-07-27 12:19:11,065 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:11] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.013 0.015
2025-07-27 12:19:11,088 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:11] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.030 0.022
2025-07-27 12:19:11,097 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:11] "GET /web/image/res.users/6/avatar_128 HTTP/1.1" 304 - 8 0.009 0.018
2025-07-27 12:19:13,954 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:13] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.020
2025-07-27 12:19:13,997 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:13] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.033 0.034
2025-07-27 12:19:14,029 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.041 0.031
2025-07-27 12:19:14,066 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /web/dataset/call_kw/hr.job/write HTTP/1.1" 200 - 8 0.015 0.050
2025-07-27 12:19:14,146 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 20 0.020 0.029
2025-07-27 12:19:14,214 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.018 0.016
2025-07-27 12:19:14,238 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.027 0.027
2025-07-27 12:19:14,249 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.036 0.043
2025-07-27 12:19:14,303 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 21 0.020 0.020
2025-07-27 12:19:14,366 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.024 0.015
2025-07-27 12:19:14,371 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.025 0.017
2025-07-27 12:19:14,373 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:14] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.033 0.018
2025-07-27 12:19:15,434 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:15] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.019
2025-07-27 12:19:15,508 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:15] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.048 0.037
2025-07-27 12:19:15,542 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:15] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.044 0.045
2025-07-27 12:19:15,556 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:15] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.060 0.058
2025-07-27 12:19:15,643 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:15] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 21 0.027 0.029
2025-07-27 12:19:15,722 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:15] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.018 0.017
2025-07-27 12:19:15,733 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:15] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.030 0.017
2025-07-27 12:19:15,733 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:15] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.023 0.021
2025-07-27 12:19:16,440 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.015 0.011
2025-07-27 12:19:16,465 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:16] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.029 0.020
2025-07-27 12:19:16,484 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:16] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.029 0.030
2025-07-27 12:19:16,484 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:16] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.034 0.031
2025-07-27 12:19:16,547 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:16] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 21 0.023 0.022
2025-07-27 12:19:16,606 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:16] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.018 0.015
2025-07-27 12:19:16,617 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:16] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.022 0.019
2025-07-27 12:19:16,619 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:16] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.029 0.022
2025-07-27 12:19:32,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:32] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.013 0.018
2025-07-27 12:19:32,247 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:32] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.032 0.036
2025-07-27 12:19:42,492 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/action/load HTTP/1.1" 200 - 13 0.013 0.022
2025-07-27 12:19:42,708 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 17 0.043 0.140
2025-07-27 12:19:42,790 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.004 0.008
2025-07-27 12:19:42,814 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.025 0.015
2025-07-27 12:19:42,948 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.066 0.037
2025-07-27 12:19:42,953 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.081 0.032
2025-07-27 12:19:42,961 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.087 0.036
2025-07-27 12:19:42,962 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 17 0.082 0.033
2025-07-27 12:19:42,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:42] "POST /web/dataset/call_kw/hr.applicant.category/read HTTP/1.1" 200 - 4 0.008 0.008
2025-07-27 12:19:56,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:56] "POST /web/action/load HTTP/1.1" 200 - 14 0.027 0.027
2025-07-27 12:19:56,270 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:56] "POST /web/dataset/call_kw/survey.survey/get_views HTTP/1.1" 200 - 44 0.053 0.108
2025-07-27 12:19:56,411 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:56] "POST /web/dataset/call_kw/survey.survey/web_search_read HTTP/1.1" 200 - 23 0.061 0.036
2025-07-27 12:19:56,588 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:56] "GET /survey/static/src/img/trophy-solid.svg HTTP/1.1" 200 - 0 0.000 0.057
2025-07-27 12:19:58,561 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:58] "POST /web/dataset/call_kw/survey.survey/read HTTP/1.1" 200 - 13 0.043 0.038
2025-07-27 12:19:58,611 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:58] "POST /web/dataset/call_kw/survey.question/read HTTP/1.1" 200 - 6 0.014 0.013
2025-07-27 12:19:58,714 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:58] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.016 0.023
2025-07-27 12:19:58,733 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:58] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.040 0.020
2025-07-27 12:19:58,744 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:19:58] "GET /web/image?model=survey.survey&id=2&field=background_image&unique=1753543799000 HTTP/1.1" 200 - 5 0.003 0.012
2025-07-27 12:20:05,546 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:20:05] "POST /web/dataset/call_kw/survey.survey/web_search_read HTTP/1.1" 200 - 13 0.020 0.024
2025-07-27 12:22:03,467 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "POST /web/action/load HTTP/1.1" 200 - 12 0.014 0.019
2025-07-27 12:22:03,528 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 4 0.000 0.032
2025-07-27 12:22:03,596 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.006 0.015
2025-07-27 12:22:03,667 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 24 0.037 0.053
2025-07-27 12:22:03,700 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.000 0.011
2025-07-27 12:22:03,919 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753618923731 HTTP/1.1" 200 - 6 0.015 0.024
2025-07-27 12:22:03,923 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753618923728 HTTP/1.1" 200 - 6 0.017 0.034
2025-07-27 12:22:03,941 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753618923731 HTTP/1.1" 200 - 6 0.017 0.039
2025-07-27 12:22:03,956 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753618923731 HTTP/1.1" 200 - 6 0.020 0.049
2025-07-27 12:22:03,985 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753618923731 HTTP/1.1" 200 - 6 0.027 0.053
2025-07-27 12:22:03,986 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:03] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753618923731 HTTP/1.1" 200 - 6 0.046 0.049
2025-07-27 12:22:04,006 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753618923734 HTTP/1.1" 200 - 6 0.015 0.052
2025-07-27 12:22:04,011 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753618923733 HTTP/1.1" 200 - 6 0.026 0.053
2025-07-27 12:22:04,024 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753618923734 HTTP/1.1" 200 - 6 0.026 0.040
2025-07-27 12:22:04,039 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753618923734 HTTP/1.1" 200 - 6 0.024 0.048
2025-07-27 12:22:04,066 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753618923735 HTTP/1.1" 200 - 6 0.021 0.038
2025-07-27 12:22:04,074 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753618923735 HTTP/1.1" 200 - 6 0.022 0.051
2025-07-27 12:22:04,091 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753618923735 HTTP/1.1" 200 - 6 0.032 0.035
2025-07-27 12:22:04,097 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753618923735 HTTP/1.1" 200 - 6 0.026 0.045
2025-07-27 12:22:04,108 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753618923735 HTTP/1.1" 200 - 6 0.017 0.035
2025-07-27 12:22:04,139 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753618923735 HTTP/1.1" 200 - 6 0.013 0.034
2025-07-27 12:22:04,139 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753618923735 HTTP/1.1" 200 - 6 0.023 0.045
2025-07-27 12:22:04,161 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753618923736 HTTP/1.1" 200 - 6 0.014 0.034
2025-07-27 12:22:04,167 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753618923736 HTTP/1.1" 200 - 6 0.016 0.024
2025-07-27 12:22:04,167 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 12:22:04] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753618923736 HTTP/1.1" 200 - 6 0.018 0.030
2025-07-27 12:29:26,104 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 12:29:26,112 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 12:29:26,128 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 12:29:26,135 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 12:30:00,880 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 12:30:00,888 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 12:30:00,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 12:30:00,920 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 12:30:00,932 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 12:30:00,943 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 15:48:40,653 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 15:48:40] "GET /websocket HTTP/1.1" 101 - 2 0.000 0.286
2025-07-27 15:49:03,219 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 15:49:03,220 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 15:49:03,230 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 15:49:03,230 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 15:49:03,230 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 15:49:03,245 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 15:49:03,253 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Update All Status`. 
2025-07-27 15:49:03,257 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Update All Status` done. 
2025-07-27 15:49:03,261 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Validate CV`. 
2025-07-27 15:49:03,261 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Validate CV` done. 
2025-07-27 15:49:03,278 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 15:49:03,286 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 15:49:03,294 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Delete Notifications older than 6 Month`. 
2025-07-27 15:49:03,294 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Delete Notifications older than 6 Month` done. 
2025-07-27 15:49:03,308 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Users: Notify About Unregistered Users`. 
2025-07-27 15:49:03,318 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Users: Notify About Unregistered Users` done. 
2025-07-27 15:49:03,325 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `HR Employee: check work permit validity`. 
2025-07-27 15:49:03,326 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `HR Employee: check work permit validity` done. 
2025-07-27 15:49:03,326 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Calendar: Event Reminder`. 
2025-07-27 15:49:03,344 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Calendar: Event Reminder` done. 
2025-07-27 15:49:03,355 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Gamification: Goal Challenge Check`. 
2025-07-27 15:49:03,403 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Gamification: Goal Challenge Check` done. 
2025-07-27 15:49:03,424 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail Marketing: Process queue`. 
2025-07-27 15:49:03,435 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail Marketing: Process queue` done. 
2025-07-27 15:49:03,435 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Project: Send rating`. 
2025-07-27 15:49:03,447 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Project: Send rating` done. 
2025-07-27 15:49:03,453 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-07-27 15:49:03,564 140048 INFO ardano_hr2 odoo.addons.base.models.ir_attachment: filestore gc 39 checked, 9 removed 
2025-07-27 15:49:03,717 140048 INFO ardano_hr2 odoo.models.unlink: User #1 deleted res.config.settings records with IDs: [1] 
2025-07-27 15:49:03,731 140048 INFO ardano_hr2 odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-07-27 15:49:03,765 140048 INFO ardano_hr2 odoo.models.unlink: User #1 deleted base.module.update records with IDs: [1, 2, 3, 4, 5, 6, 7] 
2025-07-27 15:49:03,792 140048 INFO ardano_hr2 odoo.models.unlink: User #1 deleted base.language.install records with IDs: [1] 
2025-07-27 15:49:03,829 140048 INFO ardano_hr2 odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-07-27 15:49:03,879 140048 INFO ardano_hr2 odoo.models.unlink: User #1 deleted bus.bus records with IDs: [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628] 
2025-07-27 15:49:03,940 140048 INFO ardano_hr2 odoo.models.unlink: User #1 deleted mail.compose.message records with IDs: [1, 2, 3, 4, 5, 6, 7] 
2025-07-27 15:49:04,295 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Base: Auto-vacuum internal data` done. 
2025-07-27 15:49:04,306 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 15:49:04,306 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 15:49:04,318 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 15:49:04,321 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 15:49:06,383 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 15:49:06] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.000 0.025
2025-07-27 15:49:06,395 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 15:49:06] "POST /mail/thread/data HTTP/1.1" 200 - 19 0.014 0.023
2025-07-27 16:29:25,277 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 16:29:25,287 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 16:29:25,304 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 16:29:25,319 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 16:29:45,121 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 16:29:45,144 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 16:29:45,151 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 16:29:45,167 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 16:29:45,167 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 16:29:45,199 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 16:48:58,071 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 16:48:58] "GET /websocket HTTP/1.1" 101 - 2 0.000 0.063
2025-07-27 17:29:25,950 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 17:29:25,968 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 17:29:25,989 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 17:29:25,996 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 17:29:45,361 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 17:29:45,383 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 17:29:45,393 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 17:29:45,398 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 17:29:45,414 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 17:29:45,430 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 17:30:26,030 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Digest Emails`. 
2025-07-27 17:30:26,696 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Digest Emails` done. 
2025-07-27 18:05:20,004 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 18:05:20] "GET /websocket HTTP/1.1" 101 - 2 0.000 0.016
2025-07-27 20:40:03,********** INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 20:40:03] "GET /websocket HTTP/1.1" 101 - 2 0.004 0.042
2025-07-27 20:40:41,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 20:40:41,********** WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using "YourCompany" <<EMAIL>> as fallback 
2025-07-27 20:40:41,********** WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using "YourCompany" <<EMAIL>> as fallback 
2025-07-27 20:40:45,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 20:40:45,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 20:40:46,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 20:40:46,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Website Visitor : clean inactive visitors`. 
2025-07-27 20:40:46,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Website Visitor : clean inactive visitors` done. 
2025-07-27 20:40:46,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 20:40:46,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 20:40:46,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 20:40:46,077 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 20:40:46,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 20:40:46,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 20:42:16,088 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 20:42:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.000 0.016
2025-07-27 20:42:16,107 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 20:42:16] "POST /mail/thread/data HTTP/1.1" 200 - 19 0.032 0.003
2025-07-27 20:42:16,167 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 20:42:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.015 0.000
2025-07-27 20:42:16,167 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 20:42:16] "POST /mail/thread/data HTTP/1.1" 200 - 19 0.003 0.012
2025-07-27 21:29:43,128 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 21:29:43,181 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 21:29:43,191 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 21:29:43,191 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 21:29:43,********** INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 21:29:43,216 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 21:29:43,222 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 21:29:43,225 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 21:29:43,225 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 21:29:43,235 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 22:29:43,614 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 22:29:43,645 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 22:29:43,670 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 22:29:43,683 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 22:29:43,696 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 22:29:43,712 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 22:29:43,727 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 22:29:43,731 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 22:29:43,737 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 22:29:43,746 140048 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 22:48:10,397 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 22:48:10] "GET / HTTP/1.1" 200 - 23 0.034 0.138
2025-07-27 22:48:12,246 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 22:48:12] "GET /web/static/fonts/twitter_x_only.woff HTTP/1.1" 200 - 0 0.000 0.040
2025-07-27 22:48:12,490 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 22:48:12] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.004 0.012
2025-07-27 22:56:55,485 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 22:56:55] "GET / HTTP/1.1" 200 - 23 0.066 0.061
2025-07-27 22:56:55,700 140048 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 22:56:55] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.023
2025-07-27 22:56:56,205 140048 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 22:56:56] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 4 0.004 0.005
2025-07-27 23:01:30,771 182792 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 23:01:30,771 182792 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 23:01:30,771 182792 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 23:01:30,771 182792 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 23:01:31,548 182792 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 23:01:32,298 182792 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 23:01:32,312 182792 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 23:01:32,439 182792 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 23:01:32,454 182792 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 23:01:57,030 182792 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:01:57,031 182792 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:01:58,828 182792 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 23:01:58,904 182792 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 23:01:58,954 182792 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 23:02:00,635 182792 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 23:02:00,897 182792 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 23:02:01,042 182792 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 23:02:01,459 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 23:02:01,590 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 23:02:01,631 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 23:02:01,676 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 23:02:01,714 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 23:02:01,743 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 23:02:01,767 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 23:02:01,778 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 23:02:01,790 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 23:02:01,817 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 23:02:01,833 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 23:02:01,852 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 23:02:01,885 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 23:02:01,902 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 23:02:01,913 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 23:02:02,065 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 23:02:02,134 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 23:02:02,241 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 23:02:02,313 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 23:02:02,373 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 23:02:02,401 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 23:02:02,444 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 23:02:02,509 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 23:02:02,632 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 23:02:02,684 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 23:02:02,740 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 23:02:02,766 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 23:02:02,823 182792 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 23:02:02,956 182792 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.32s, 908 queries (+908 other) 
2025-07-27 23:02:02,956 182792 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 4.05s, 908 queries (+908 extra) 
2025-07-27 23:02:04,085 182792 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 23:02:04,090 182792 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 31.936s 
2025-07-27 23:02:20,174 198324 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 23:02:20,176 198324 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 23:02:20,176 198324 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 23:02:20,176 198324 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 23:02:20,371 198324 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 23:02:20,675 198324 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 23:02:20,681 198324 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 23:02:20,747 198324 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 23:02:20,753 198324 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 23:02:21,874 198324 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 23:02:23,044 198324 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:02:23,045 198324 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:02:24,458 198324 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 23:02:24,503 198324 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 23:02:24,524 198324 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 23:02:25,538 198324 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 23:02:25,672 198324 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 23:02:25,763 198324 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 23:02:26,025 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 23:02:26,097 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 23:02:26,114 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 23:02:26,133 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 23:02:26,151 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 23:02:26,160 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 23:02:26,168 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 23:02:26,174 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 23:02:26,183 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 23:02:26,196 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 23:02:26,206 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 23:02:26,215 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 23:02:26,244 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 23:02:26,250 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 23:02:26,256 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 23:02:26,358 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 23:02:26,404 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 23:02:26,423 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 23:02:26,450 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 23:02:26,489 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 23:02:26,507 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 23:02:26,531 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 23:02:26,631 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 23:02:26,747 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 23:02:26,774 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 23:02:26,806 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 23:02:26,818 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 23:02:26,857 198324 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 23:02:27,144 198324 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 1.61s, 908 queries (+908 other) 
2025-07-27 23:02:27,144 198324 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 2.64s, 908 queries (+908 extra) 
2025-07-27 23:02:28,139 198324 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 23:02:28,147 198324 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 7.542s 
2025-07-27 23:04:34,029 145556 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 23:04:34,029 145556 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 23:04:34,029 145556 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 23:04:34,029 145556 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 23:04:34,196 145556 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 23:04:34,356 145556 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 23:04:34,362 145556 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 23:04:34,411 145556 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 23:04:34,418 145556 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 23:04:35,586 145556 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 23:04:35,999 145556 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:04:35,999 145556 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:04:36,607 145556 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 23:04:36,645 145556 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 23:04:36,657 145556 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 23:04:37,143 145556 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 23:04:37,208 145556 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 23:04:37,259 145556 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 23:04:37,440 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 23:04:37,475 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 23:04:37,475 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 23:04:37,483 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 23:04:37,490 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 23:04:37,490 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 23:04:37,490 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 23:04:37,497 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 23:04:37,497 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 23:04:37,504 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 23:04:37,504 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 23:04:37,504 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 23:04:37,511 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 23:04:37,511 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 23:04:37,518 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 23:04:37,561 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 23:04:37,583 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 23:04:37,595 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 23:04:37,605 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 23:04:37,627 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 23:04:37,634 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 23:04:37,648 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 23:04:37,677 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 23:04:37,727 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 23:04:37,741 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 23:04:37,763 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 23:04:37,770 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 23:04:37,784 145556 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 23:04:37,835 145556 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 0.69s, 908 queries (+908 other) 
2025-07-27 23:04:37,835 145556 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 1.19s, 908 queries (+908 extra) 
2025-07-27 23:04:38,415 145556 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 23:04:38,427 145556 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 4.113s 
2025-07-27 23:04:38,526 145556 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 23:04:40,409 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:04:40] "GET / HTTP/1.1" 200 - 209 0.137 2.105
2025-07-27 23:04:41,150 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:04:41] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 10 0.011 0.225
2025-07-27 23:09:50,704 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:50] "GET / HTTP/1.1" 200 - 124 0.101 0.326
2025-07-27 23:09:50,825 145556 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:50] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.009
2025-07-27 23:09:50,930 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:50] "GET /web/assets/748-f2c213f/1/web.assets_frontend.min.css HTTP/1.1" 200 - 7 0.006 0.103
2025-07-27 23:09:50,980 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:50] "GET /web/assets/751-aafacf8/1/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 7 0.024 0.129
2025-07-27 23:09:52,498 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:52] "GET /web/image/website/1/favicon?unique=77521d1 HTTP/1.1" 200 - 9 0.041 0.007
2025-07-27 23:09:52,750 145556 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:52] "GET /web/static/fonts/twitter_x_only.woff HTTP/1.1" 200 - 0 0.000 0.009
2025-07-27 23:09:52,779 145556 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:52] "GET /web/static/img/odoo_logo_tiny.png HTTP/1.1" 200 - 0 0.000 0.003
2025-07-27 23:09:52,803 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:52] "GET /web/image/website/1/logo/My%20Website?unique=77521d1 HTTP/1.1" 200 - 7 0.006 0.024
2025-07-27 23:09:54,088 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:54] "GET /web/assets/752-5c3e5e4/1/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 7 0.002 0.362
2025-07-27 23:09:54,608 145556 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:09:54] "GET /website/translations/b656ccc949500002b39a5160e5ba1db4a1cee63e?lang=en_US HTTP/1.1" 200 - 5 0.020 0.005
2025-07-27 23:10:53,902 171924 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 23:10:53,902 171924 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 23:10:53,902 171924 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 23:10:53,902 171924 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 23:10:54,145 171924 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 23:10:54,422 171924 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 23:10:54,422 171924 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 23:10:54,470 171924 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 23:10:54,472 171924 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 23:10:56,984 171924 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:10:56,984 171924 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:10:58,579 171924 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 23:10:58,668 171924 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 23:10:58,721 171924 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 23:11:00,127 171924 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 23:11:00,299 171924 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 23:11:00,503 171924 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 23:11:00,863 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 23:11:00,950 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 23:11:00,960 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 23:11:00,970 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 23:11:00,977 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 23:11:00,983 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 23:11:00,989 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 23:11:00,993 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 23:11:00,998 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 23:11:01,008 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 23:11:01,020 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 23:11:01,025 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 23:11:01,040 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 23:11:01,055 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 23:11:01,059 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 23:11:01,149 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 23:11:01,201 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 23:11:01,224 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 23:11:01,246 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 23:11:01,281 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 23:11:01,298 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 23:11:01,319 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 23:11:01,384 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 23:11:01,502 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 23:11:01,542 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 23:11:01,582 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 23:11:01,593 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 23:11:01,634 171924 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 23:11:01,781 171924 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 1.65s, 908 queries (+908 other) 
2025-07-27 23:11:01,781 171924 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 3.11s, 908 queries (+908 extra) 
2025-07-27 23:11:02,766 171924 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 23:11:02,777 171924 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 8.464s 
2025-07-27 23:18:02,116 172028 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 23:18:02,117 172028 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 23:18:02,117 172028 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 23:18:02,117 172028 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 23:18:02,240 172028 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 23:18:02,435 172028 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 23:18:02,439 172028 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 23:18:02,490 172028 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 23:18:02,495 172028 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 23:18:04,290 172028 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:18:04,290 172028 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:18:05,334 172028 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 23:18:05,370 172028 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 23:18:05,384 172028 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 23:18:06,198 172028 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 23:18:06,270 172028 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 23:18:06,327 172028 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 23:18:06,499 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 23:18:06,530 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 23:18:06,535 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 23:18:06,540 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 23:18:06,543 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 23:18:06,547 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 23:18:06,552 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 23:18:06,556 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 23:18:06,561 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 23:18:06,572 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 23:18:06,577 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 23:18:06,581 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 23:18:06,590 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 23:18:06,592 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 23:18:06,596 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 23:18:06,674 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 23:18:06,738 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 23:18:06,762 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 23:18:06,786 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 23:18:06,827 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 23:18:06,842 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 23:18:06,859 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 23:18:06,894 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 23:18:06,958 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 23:18:06,995 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 23:18:07,033 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 23:18:07,042 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 23:18:07,063 172028 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 23:18:07,158 172028 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 0.96s, 908 queries (+908 other) 
2025-07-27 23:18:07,158 172028 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 1.79s, 908 queries (+908 extra) 
2025-07-27 23:18:07,719 172028 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 23:18:07,724 172028 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 5.354s 
2025-07-27 23:18:26,277 162444 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 23:18:26,277 162444 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 23:18:26,277 162444 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 23:18:26,278 162444 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 23:18:26,434 162444 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 23:18:26,784 162444 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 23:18:26,790 162444 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 23:18:26,843 162444 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 23:18:26,849 162444 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 23:18:28,004 162444 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 23:18:28,017 162444 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:18:28,017 162444 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:18:28,823 162444 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 23:18:28,857 162444 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 23:18:28,872 162444 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 23:18:29,378 162444 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 23:18:29,451 162444 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 23:18:29,497 162444 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 23:18:29,623 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 23:18:29,648 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 23:18:29,657 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 23:18:29,661 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 23:18:29,666 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 23:18:29,671 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 23:18:29,674 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 23:18:29,676 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 23:18:29,679 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 23:18:29,683 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 23:18:29,687 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 23:18:29,689 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 23:18:29,697 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 23:18:29,699 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 23:18:29,702 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 23:18:29,761 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 23:18:29,790 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 23:18:29,803 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 23:18:29,819 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 23:18:29,849 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 23:18:29,864 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 23:18:29,880 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 23:18:29,921 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 23:18:29,992 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 23:18:30,009 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 23:18:30,026 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 23:18:30,032 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 23:18:30,051 162444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 23:18:30,135 162444 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 0.76s, 908 queries (+908 other) 
2025-07-27 23:18:30,136 162444 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 1.28s, 908 queries (+908 extra) 
2025-07-27 23:18:30,582 162444 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 23:18:30,586 162444 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 3.857s 
2025-07-27 23:18:30,640 162444 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 23:18:32,113 162444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:18:32] "GET / HTTP/1.1" 200 - 209 0.103 3.976
2025-07-27 23:18:32,714 162444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 23:18:32] "POST /web/dataset/call_kw/web_tour.tour/get_consumed_tours HTTP/1.1" 200 - 10 0.006 0.221
2025-07-27 23:26:03,147 9996 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 23:26:03,147 9996 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 23:26:03,149 9996 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 23:26:03,149 9996 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 23:26:03,316 9996 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 23:26:03,489 9996 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU.nm.internal:8090 
2025-07-27 23:26:03,519 9996 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 23:26:03,526 9996 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 23:26:03,564 9996 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 23:26:03,569 9996 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 23:26:04,749 9996 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:26:04,749 9996 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:26:05,580 9996 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 23:26:05,614 9996 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 23:26:05,639 9996 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 23:26:06,249 9996 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 23:26:06,319 9996 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 23:26:06,377 9996 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 23:26:06,562 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 23:26:06,579 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 23:26:06,590 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 23:26:06,595 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 23:26:06,595 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 23:26:06,599 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 23:26:06,599 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 23:26:06,599 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 23:26:06,610 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 23:26:06,611 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 23:26:06,620 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 23:26:06,620 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 23:26:06,631 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 23:26:06,631 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 23:26:06,631 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 23:26:06,691 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 23:26:06,721 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 23:26:06,729 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 23:26:06,739 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 23:26:06,759 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 23:26:06,769 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 23:26:06,784 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 23:26:06,799 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 23:26:06,840 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 23:26:06,849 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 23:26:06,864 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 23:26:06,869 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 23:26:06,890 9996 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 23:26:07,110 9996 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 0.86s, 908 queries (+908 other) 
2025-07-27 23:26:07,110 9996 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 1.50s, 908 queries (+908 extra) 
2025-07-27 23:26:07,890 9996 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 23:26:07,904 9996 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 4.424s 
2025-07-27 23:26:11,102 9996 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 23:26:13,570 9996 INFO ardano_hr2 werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:13] "GET / HTTP/1.1" 200 - 152 0.135 2.351
2025-07-27 23:26:14,190 9996 INFO ? werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:14] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.406
2025-07-27 23:26:14,203 9996 INFO ardano_hr2 werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:14] "GET /web/assets/748-f2c213f/1/web.assets_frontend.min.css HTTP/1.1" 200 - 7 0.006 0.407
2025-07-27 23:26:14,209 9996 INFO ardano_hr2 werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:14] "GET /web/assets/751-aafacf8/1/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 7 0.007 0.412
2025-07-27 23:26:19,450 9996 INFO ardano_hr2 werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:19] "GET /web/image/website/1/favicon?unique=77521d1 HTTP/1.1" 200 - 9 0.010 0.022
2025-07-27 23:26:19,810 9996 INFO ? werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:19] "GET /web/static/fonts/twitter_x_only.woff HTTP/1.1" 200 - 0 0.000 0.004
2025-07-27 23:26:19,865 9996 INFO ? werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:19] "GET /web/static/img/odoo_logo_tiny.png HTTP/1.1" 200 - 0 0.000 0.004
2025-07-27 23:26:19,887 9996 INFO ardano_hr2 werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:19] "GET /web/image/website/1/logo/My%20Website?unique=77521d1 HTTP/1.1" 200 - 7 0.007 0.023
2025-07-27 23:26:21,242 9996 INFO ardano_hr2 werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:21] "GET /web/assets/752-5c3e5e4/1/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 7 0.013 0.009
2025-07-27 23:26:21,473 9996 INFO ardano_hr2 werkzeug: 10.10.1.1 - - [27/Jul/2025 23:26:21] "GET /website/translations/b656ccc949500002b39a5160e5ba1db4a1cee63e?lang=en_US HTTP/1.1" 200 - 5 0.005 0.012
2025-07-27 23:26:47,527 9996 INFO ardano_hr2 werkzeug: 10.10.1.2 - - [27/Jul/2025 23:26:47] "GET / HTTP/1.1" 200 - 8 0.007 0.023
2025-07-27 23:27:09,299 9996 INFO ? werkzeug: 10.10.1.2 - - [27/Jul/2025 23:27:09] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.000
2025-07-27 23:29:08,072 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-27 23:29:08,096 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-07-27 23:29:08,121 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-27 23:29:08,138 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-07-27 23:30:08,199 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-27 23:30:08,230 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-07-27 23:30:08,243 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-07-27 23:30:08,250 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-07-27 23:30:08,266 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-27 23:30:08,277 9996 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-07-27 23:30:43,600 9996 INFO ardano_hr2 werkzeug: 10.10.1.2 - - [27/Jul/2025 23:30:43] "GET / HTTP/1.1" 200 - 7 0.037 0.023
2025-07-27 23:30:44,780 9996 INFO ardano_hr2 werkzeug: 10.10.1.2 - - [27/Jul/2025 23:30:44] "GET /web/assets/748-f2c213f/1/web.assets_frontend.min.css HTTP/1.1" 200 - 4 0.004 0.007
2025-07-27 23:31:04,240 9996 INFO ? werkzeug: 10.10.1.2 - - [27/Jul/2025 23:31:04] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.006
2025-07-27 23:35:01,215 154732 INFO ? odoo: Odoo version 16.0-******** 
2025-07-27 23:35:01,215 154732 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 23:35:01,215 154732 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 23:35:01,215 154732 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 23:35:01,408 154732 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 23:35:01,733 154732 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 23:35:01,737 154732 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 23:35:01,787 154732 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 23:35:01,795 154732 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 23:35:02,912 154732 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 23:35:03,202 154732 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:35:03,202 154732 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 23:35:03,875 154732 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 23:35:03,898 154732 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 23:35:03,911 154732 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 23:35:04,461 154732 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 23:35:04,551 154732 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 23:35:04,601 154732 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 23:35:04,748 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 23:35:04,779 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 23:35:04,785 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 23:35:04,791 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 23:35:04,795 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 23:35:04,798 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 23:35:04,801 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 23:35:04,802 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 23:35:04,804 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 23:35:04,808 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 23:35:04,813 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 23:35:04,815 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 23:35:04,824 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 23:35:04,826 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 23:35:04,828 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 23:35:04,876 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 23:35:04,898 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 23:35:04,907 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 23:35:04,916 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 23:35:04,934 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 23:35:04,943 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 23:35:04,950 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 23:35:04,972 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 23:35:05,015 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 23:35:05,027 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 23:35:05,038 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 23:35:05,045 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 23:35:05,058 154732 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 23:35:05,120 154732 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 0.66s, 908 queries (+908 other) 
2025-07-27 23:35:05,120 154732 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 1.22s, 908 queries (+908 extra) 
2025-07-27 23:35:05,543 154732 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 23:35:05,556 154732 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 3.901s 
