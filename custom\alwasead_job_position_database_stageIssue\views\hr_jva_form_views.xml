<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- JVA Form View -->
        <record id="view_hr_jva_form_form" model="ir.ui.view">
            <field name="name">hr.jva.form.form</field>
            <field name="model">hr.jva.form</field>
            <field name="arch" type="xml">
                <form string="Job Vacancy Announcement Form">
                    <header>
                        <button name="action_request_approval" type="object" 
                                string="Request Approval" class="btn-primary"
                                attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('can_request_approval', '=', False)]}"/>
                        <button name="action_approve" type="object"
                                string="Approve JVA" class="btn-success"
                                attrs="{'invisible': [('state', '!=', 'approval_pending')]}"
                                groups="alwasead_job_position_database.group_jva_approver"/>
                        <button name="action_reject" type="object"
                                string="Reject JVA" class="btn-danger"
                                attrs="{'invisible': [('state', '!=', 'approval_pending')]}"
                                groups="alwasead_job_position_database.group_jva_approver"/>
                        <button name="action_view_job_advertisement" type="object"
                                string="View Job Advertisement" class="btn-info"
                                attrs="{'invisible': [('job_advertisement_created', '=', False)]}"
                                icon="fa-external-link"/>
                        <button name="action_view_job_applications" type="object"
                                string="View Applications" class="btn-secondary"
                                attrs="{'invisible': [('job_advertisement_created', '=', False)]}"
                                icon="fa-users"/>
                        <button name="action_reset_to_draft" type="object" 
                                string="Reset to Draft" class="btn-secondary"
                                attrs="{'invisible': [('state', '!=', 'rejected')]}"/>
                        <button name="action_cancel" type="object" 
                                string="Cancel" class="btn-secondary"
                                attrs="{'invisible': [('state', 'in', ['approved', 'cancelled'])]}"/>
                        <field name="state" widget="statusbar" 
                               statusbar_visible="draft,approval_pending,approved"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="Job Position Information (معلومات المنصب)">
                                <field name="job_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="job_title"/>
                                <field name="division_id"/>
                                <field name="department_id"/>
                                <field name="unit_id"/>
                                <field name="job_category_id"/>
                            </group>
                            <group string="Request Information (معلومات الطلب)">
                                <field name="requested_positions" attrs="{'readonly': [('state', 'not in', ['draft', 'rejected'])]}"/>
                                <field name="suggested_positions"/>
                                <field name="priority" attrs="{'readonly': [('state', 'not in', ['draft', 'rejected'])]}"/>
                                <field name="expected_start_date" attrs="{'readonly': [('state', 'not in', ['draft', 'rejected'])]}"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Requirements (المتطلبات)" name="requirements">
                                <group>
                                    <field name="minimum_requirements" readonly="1"/>
                                    <field name="additional_qualifications" 
                                           attrs="{'readonly': [('state', 'not in', ['draft', 'rejected'])]}"/>
                                    <field name="language_requirements" 
                                           attrs="{'readonly': [('state', 'not in', ['draft', 'rejected'])]}"/>
                                    <field name="experience_level" 
                                           attrs="{'readonly': [('state', 'not in', ['draft', 'rejected'])]}"/>
                                </group>
                            </page>
                            
                            <page string="Justification (المبررات)" name="justification">
                                <field name="justification" 
                                       attrs="{'readonly': [('state', 'not in', ['draft', 'rejected'])]}"
                                       placeholder="Please provide detailed justification for this recruitment request..."/>
                            </page>
                            
                            <page string="Tracking (التتبع)" name="tracking">
                                <group>
                                    <group string="Request Details">
                                        <field name="requested_by"/>
                                        <field name="request_date"/>
                                        <field name="submitted_date"/>
                                    </group>
                                    <group string="Review Details">
                                        <field name="reviewed_by"/>
                                        <field name="review_date"/>
                                        <field name="approved_by"/>
                                        <field name="approval_date"/>
                                        <field name="rejected_by"/>
                                        <field name="rejection_date"/>
                                    </group>
                                    <group string="Job Advertisement" attrs="{'invisible': [('job_advertisement_created', '=', False)]}">
                                        <field name="job_advertisement_created"/>
                                        <field name="job_advertisement_url" widget="url"/>
                                        <field name="job_applications_count"/>
                                    </group>
                                </group>
                                <group string="Rejection Reason" attrs="{'invisible': [('rejection_reason', '=', False)]}">
                                    <field name="rejection_reason" nolabel="1"/>
                                </group>
                            </page>
                        </notebook>
                        
                        <!-- Hidden fields for computations -->
                        <field name="can_request_approval" invisible="1"/>
                        <field name="is_editable" invisible="1"/>
                    </sheet>
                    
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- JVA Form Tree View -->
        <record id="view_hr_jva_form_tree" model="ir.ui.view">
            <field name="name">hr.jva.form.tree</field>
            <field name="model">hr.jva.form</field>
            <field name="arch" type="xml">
                <tree string="JVA Forms" decoration-info="state == 'draft'" 
                      decoration-warning="state == 'approval_pending'" 
                      decoration-success="state == 'approved'"
                      decoration-danger="state == 'rejected'">
                    <field name="name"/>
                    <field name="job_title"/>
                    <field name="division_id"/>
                    <field name="department_id"/>
                    <field name="requested_positions"/>
                    <field name="priority" widget="badge"/>
                    <field name="requested_by"/>
                    <field name="request_date"/>
                    <field name="state" widget="badge" 
                           decoration-info="state == 'draft'"
                           decoration-warning="state == 'approval_pending'"
                           decoration-success="state == 'approved'"
                           decoration-danger="state == 'rejected'"/>
                </tree>
            </field>
        </record>

        <!-- JVA Form Search View -->
        <record id="view_hr_jva_form_search" model="ir.ui.view">
            <field name="name">hr.jva.form.search</field>
            <field name="model">hr.jva.form</field>
            <field name="arch" type="xml">
                <search string="JVA Forms">
                    <field name="name"/>
                    <field name="job_title"/>
                    <field name="division_id"/>
                    <field name="department_id"/>
                    <field name="requested_by"/>
                    
                    <filter string="My Requests" name="my_requests"
                            domain="[('requested_by', '=', uid)]"/>
                    <separator/>
                    <filter string="Draft" name="draft" 
                            domain="[('state', '=', 'draft')]"/>
                    <filter string="Pending Approval" name="pending" 
                            domain="[('state', '=', 'approval_pending')]"/>
                    <filter string="Approved" name="approved" 
                            domain="[('state', '=', 'approved')]"/>
                    <filter string="Rejected" name="rejected" 
                            domain="[('state', '=', 'rejected')]"/>
                    <separator/>
                    <filter string="High Priority" name="high_priority" 
                            domain="[('priority', 'in', ['high', 'urgent'])]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_state" 
                                context="{'group_by': 'state'}"/>
                        <filter string="Division" name="group_division" 
                                context="{'group_by': 'division_id'}"/>
                        <filter string="Department" name="group_department" 
                                context="{'group_by': 'department_id'}"/>
                        <filter string="Priority" name="group_priority" 
                                context="{'group_by': 'priority'}"/>
                        <filter string="Requested By" name="group_requested_by" 
                                context="{'group_by': 'requested_by'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- JVA Form Action -->
        <record id="action_hr_jva_form" model="ir.actions.act_window">
            <field name="name">JVA Forms</field>
            <field name="res_model">hr.jva.form</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Job Vacancy Announcement (JVA) Form
                </p>
                <p>
                    JVA Forms are used to request approval for new recruitment.
                    They must be approved by the General Manager before recruitment can begin.
                </p>
            </field>
        </record>

        <!-- Specific actions for menu items with context -->
        <record id="action_hr_jva_form_my" model="ir.actions.act_window">
            <field name="name">My JVA Requests</field>
            <field name="res_model">hr.jva.form</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'search_default_my_requests': 1}</field>
        </record>

        <record id="action_hr_jva_form_pending" model="ir.actions.act_window">
            <field name="name">Pending Approvals</field>
            <field name="res_model">hr.jva.form</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'search_default_pending': 1}</field>
        </record>

    </data>
</odoo>
