<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- JVA Approval Request Email Template -->
        <record id="email_template_jva_approval_request" model="mail.template">
            <field name="name">JVA Approval Request</field>
            <field name="model_id" ref="model_hr_jva_form"/>
            <field name="subject">JVA Approval Required: ${object.job_title}</field>
            <field name="email_from"><EMAIL></field>
            <field name="email_to">${object.env['hr.employee'].search([('is_general_manager', '=', True)], limit=1).work_email or ''}</field>
            <field name="description">Sent to General Manager when JVA form is submitted for approval</field>
            <field name="body_html">
                &lt;p&gt;Dear General Manager,&lt;/p&gt;
                &lt;p&gt;A new JVA form requires your approval:&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;JVA Reference:&lt;/strong&gt; ${object.name}&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Job Position:&lt;/strong&gt; ${object.job_title}&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Department:&lt;/strong&gt; ${object.department_id.name or 'N/A'}&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Requested Positions:&lt;/strong&gt; ${object.requested_positions}&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Requested By:&lt;/strong&gt; ${object.requested_by.name or 'N/A'}&lt;/p&gt;
                &lt;p&gt;Please review and approve/reject this request in the system.&lt;/p&gt;
                &lt;p&gt;Best regards,&lt;br/&gt;HR System&lt;/p&gt;
            </field>
        </record>
        
    </data>
</odoo>
