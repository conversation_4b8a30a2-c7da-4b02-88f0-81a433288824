<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Job Categories based on AlWasead requirements -->
        <record id="job_category_engineer" model="hr.job.category">
            <field name="name">Engineer</field>
            <field name="code">ENG</field>
            <field name="sequence">10</field>
            <field name="minimum_education_level">bachelor</field>
            <field name="minimum_experience_years">0</field>
            <field name="education_requirements">Bachelor's degree in Engineering (Civil, Mechanical, Electrical, or related field). Professional engineering license preferred.</field>
            <field name="description">Engineering positions requiring technical expertise and problem-solving skills</field>
        </record>

        <record id="job_category_financial_officer" model="hr.job.category">
            <field name="name">Financial Officer</field>
            <field name="code">FIN</field>
            <field name="sequence">20</field>
            <field name="minimum_education_level">bachelor</field>
            <field name="minimum_experience_years">2</field>
            <field name="required_field_experience">True</field>
            <field name="education_requirements">Bachelor's degree in Finance, Accounting, Economics, or related field. CPA or CFA certification preferred.</field>
            <field name="description">Financial management and accounting positions</field>
        </record>

        <record id="job_category_project_manager" model="hr.job.category">
            <field name="name">Project Manager</field>
            <field name="code">PM</field>
            <field name="sequence">30</field>
            <field name="minimum_education_level">bachelor</field>
            <field name="minimum_experience_years">3</field>
            <field name="required_field_experience">True</field>
            <field name="education_requirements">Bachelor's degree in Engineering, Business Administration, or related field. PMP certification preferred.</field>
            <field name="description">Project management positions requiring leadership and coordination skills</field>
        </record>

        <record id="job_category_admin_officer" model="hr.job.category">
            <field name="name">Administrative Officer</field>
            <field name="code">ADM</field>
            <field name="sequence">40</field>
            <field name="minimum_education_level">diploma</field>
            <field name="minimum_experience_years">1</field>
            <field name="education_requirements">High Diploma or Bachelor's degree in Business Administration or related field.</field>
            <field name="description">Administrative and support positions</field>
        </record>

        <record id="job_category_hr_officer" model="hr.job.category">
            <field name="name">HR Officer</field>
            <field name="code">HR</field>
            <field name="sequence">50</field>
            <field name="minimum_education_level">bachelor</field>
            <field name="minimum_experience_years">2</field>
            <field name="required_field_experience">True</field>
            <field name="education_requirements">Bachelor's degree in Human Resources, Psychology, Business Administration, or related field.</field>
            <field name="description">Human resources management positions</field>
        </record>

        <record id="job_category_it_specialist" model="hr.job.category">
            <field name="name">IT Specialist</field>
            <field name="code">IT</field>
            <field name="sequence">60</field>
            <field name="minimum_education_level">bachelor</field>
            <field name="minimum_experience_years">1</field>
            <field name="required_field_experience">True</field>
            <field name="education_requirements">Bachelor's degree in Computer Science, Information Technology, or related field. Relevant certifications preferred.</field>
            <field name="description">Information technology and systems positions</field>
        </record>

        <record id="job_category_safety_officer" model="hr.job.category">
            <field name="name">Safety Officer</field>
            <field name="code">SAF</field>
            <field name="sequence">70</field>
            <field name="minimum_education_level">bachelor</field>
            <field name="minimum_experience_years">2</field>
            <field name="required_field_experience">True</field>
            <field name="education_requirements">Bachelor's degree in Safety Engineering, Environmental Science, or related field. Safety certifications required.</field>
            <field name="description">Health, safety, and environmental positions</field>
        </record>

        <record id="job_category_quality_officer" model="hr.job.category">
            <field name="name">Quality Officer</field>
            <field name="code">QUA</field>
            <field name="sequence">80</field>
            <field name="minimum_education_level">bachelor</field>
            <field name="minimum_experience_years">2</field>
            <field name="required_field_experience">True</field>
            <field name="education_requirements">Bachelor's degree in Engineering, Quality Management, or related field. Quality certifications preferred.</field>
            <field name="description">Quality assurance and control positions</field>
        </record>

        <record id="job_category_technician" model="hr.job.category">
            <field name="name">Technician</field>
            <field name="code">TEC</field>
            <field name="sequence">90</field>
            <field name="minimum_education_level">diploma</field>
            <field name="minimum_experience_years">1</field>
            <field name="required_field_experience">True</field>
            <field name="education_requirements">Technical diploma or certificate in relevant field. Hands-on experience required.</field>
            <field name="description">Technical and maintenance positions</field>
        </record>

        <record id="job_category_supervisor" model="hr.job.category">
            <field name="name">Supervisor</field>
            <field name="code">SUP</field>
            <field name="sequence">100</field>
            <field name="minimum_education_level">diploma</field>
            <field name="minimum_experience_years">3</field>
            <field name="required_field_experience">True</field>
            <field name="education_requirements">High Diploma or Bachelor's degree with significant supervisory experience in relevant field.</field>
            <field name="description">Supervisory and team leadership positions</field>
        </record>

    </data>
</odoo>
